import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/security_utils.dart';

/// العقل الاصطناعي لنظام EduTrack AI
/// يتولى فهم اللغة الطبيعية والتفكير والاستنتاج
class AIBrain {
  /// نماذج الذكاء الاصطناعي
  GenerativeModel? _understandingModel;
  GenerativeModel? _reasoningModel;
  
  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();
  
  /// قاموس المفاهيم التعليمية
  static const Map<String, List<String>> _educationalConcepts = {
    'طالب': ['طلاب', 'تلميذ', 'تلاميذ', 'دارس', 'دارسين', 'متعلم', 'متعلمين'],
    'مجموعة': ['مجموعات', 'فصل', 'فصول', 'كلاس', 'صف', 'صفوف', 'قسم'],
    'درس': ['دروس', 'حصة', 'حصص', 'محاضرة', 'محاضرات', 'جلسة', 'جلسات'],
    'حضور': ['غياب', 'موجود', 'غائب', 'حاضر', 'متغيب'],
    'دفع': ['مدفوع', 'غير مدفوع', 'رسوم', 'مصاريف', 'أقساط'],
    'إضافة': ['أضف', 'أنشئ', 'اعمل', 'كون', 'سجل', 'أدخل'],
    'حذف': ['احذف', 'امسح', 'أزل', 'ألغ', 'شيل'],
    'تعديل': ['عدل', 'غير', 'حدث', 'طور', 'صحح'],
    'عرض': ['اعرض', 'أظهر', 'وضح', 'اعطني', 'أريد'],
    'إحصائيات': ['إحصائية', 'تقرير', 'معدل', 'نسبة', 'عدد', 'كم'],
  };

  /// تهيئة العقل الاصطناعي
  Future<void> initialize() async {
    try {
      // نموذج الفهم - سريع ودقيق
      _understandingModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.3,
          topK: 20,
          topP: 0.8,
          maxOutputTokens: 1024,
        ),
      );

      // نموذج التفكير - متقدم وعميق
      _reasoningModel = GenerativeModel(
        model: 'gemini-1.5-pro',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4096,
        ),
      );

      debugPrint('🧠 تم تهيئة العقل الاصطناعي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة العقل الاصطناعي: $e');
      rethrow;
    }
  }

  /// فهم الرسالة والسياق
  Future<AIUnderstanding> understand(
    String message,
    Map<String, dynamic>? context,
  ) async {
    try {
      // 1. التحليل السريع المحلي
      final quickAnalysis = _performQuickAnalysis(message);
      
      // 2. الفهم العميق بالذكاء الاصطناعي
      final deepUnderstanding = await _performDeepUnderstanding(message, context);
      
      // 3. دمج النتائج
      return _mergeUnderstanding(quickAnalysis, deepUnderstanding, message);
    } catch (e) {
      debugPrint('❌ خطأ في فهم الرسالة: $e');
      return _fallbackUnderstanding(message);
    }
  }

  /// التفكير واتخاذ القرار
  Future<AIDecision> reason(
    AIUnderstanding understanding,
    Map<String, dynamic> context,
  ) async {
    try {
      if (!understanding.requiresReasoning) {
        return AIDecision.simple(understanding.intent);
      }

      final reasoningPrompt = _buildReasoningPrompt(understanding, context);
      
      final content = [Content.text(reasoningPrompt)];
      final response = await _reasoningModel!.generateContent(content);
      
      return _parseReasoningResponse(response.text ?? '', understanding);
    } catch (e) {
      debugPrint('❌ خطأ في التفكير: $e');
      return AIDecision.fallback(understanding.intent);
    }
  }

  /// التحليل السريع المحلي
  AIUnderstanding _performQuickAnalysis(String message) {
    final normalizedMessage = message.toLowerCase().trim();
    
    // تحديد النوع
    final type = _detectMessageType(normalizedMessage);
    
    // تحديد النية
    final intent = _detectIntent(normalizedMessage, type);
    
    // استخراج الكيانات
    final entities = _extractEntities(normalizedMessage);
    
    // تحديد مستوى التعقيد
    final complexity = _assessComplexity(normalizedMessage);
    
    // تحديد ما إذا كان يتطلب تفكير
    final requiresReasoning = _requiresReasoning(normalizedMessage, complexity);
    
    return AIUnderstanding(
      originalMessage: message,
      type: type,
      intent: intent,
      entities: entities,
      complexity: complexity,
      requiresReasoning: requiresReasoning,
      confidence: 0.7, // سيتم تحديثها بالفهم العميق
      keywords: _extractKeywords(normalizedMessage),
      sentiment: _analyzeSentiment(normalizedMessage),
    );
  }

  /// الفهم العميق بالذكاء الاصطناعي
  Future<Map<String, dynamic>> _performDeepUnderstanding(
    String message,
    Map<String, dynamic>? context,
  ) async {
    final prompt = _buildUnderstandingPrompt(message, context);
    
    try {
      final content = [Content.text(prompt)];
      final response = await _understandingModel!.generateContent(content);
      
      return _parseUnderstandingResponse(response.text ?? '');
    } catch (e) {
      debugPrint('❌ خطأ في الفهم العميق: $e');
      return {};
    }
  }

  /// بناء برومبت الفهم
  String _buildUnderstandingPrompt(String message, Map<String, dynamic>? context) {
    return '''
أنت مساعد ذكي متخصص في أنظمة إدارة التعليم. حلل هذه الرسالة بعمق:

الرسالة: "$message"

${context != null ? 'السياق: ${jsonEncode(context)}' : ''}

حلل الرسالة وحدد:
1. نوع الرسالة (سؤال، طلب، أمر، محادثة)
2. النية الحقيقية للمستخدم
3. الكيانات التعليمية المذكورة (طلاب، مجموعات، دروس، إلخ)
4. مستوى التعقيد (بسيط، متوسط، معقد)
5. هل يتطلب تفكير متقدم؟
6. المشاعر (إيجابي، سلبي، محايد)
7. الكلمات المفتاحية المهمة
8. مستوى الثقة في التحليل

أجب بتنسيق JSON:
{
  "type": "نوع الرسالة",
  "intent": "النية",
  "entities": ["كيان1", "كيان2"],
  "complexity": "مستوى التعقيد",
  "requires_reasoning": true/false,
  "sentiment": "المشاعر",
  "keywords": ["كلمة1", "كلمة2"],
  "confidence": 0.95,
  "explanation": "شرح التحليل"
}
''';
  }

  /// بناء برومبت التفكير
  String _buildReasoningPrompt(AIUnderstanding understanding, Map<String, dynamic> context) {
    return '''
أنت مساعد ذكي متخصص في أنظمة إدارة التعليم. فكر بعمق في هذا الطلب:

فهم الرسالة: ${jsonEncode(understanding.toJson())}

السياق الكامل: ${jsonEncode(context)}

مهمتك:
1. تحليل الطلب بعمق
2. تحديد الخطوات المطلوبة
3. اتخاذ القرار الأنسب
4. تحديد الإجراءات اللازمة

فكر خطوة بخطوة وحدد:
- ما هو الهدف الحقيقي؟
- ما هي الخطوات المطلوبة؟
- ما هي البيانات المطلوبة؟
- ما هي الإجراءات اللازمة؟

أجب بتنسيق JSON:
{
  "goal": "الهدف الحقيقي",
  "steps": ["خطوة1", "خطوة2"],
  "required_data": ["بيان1", "بيان2"],
  "actions": [
    {
      "type": "نوع الإجراء",
      "target": "الهدف",
      "parameters": {}
    }
  ],
  "reasoning": "شرح التفكير"
}
''';
  }

  /// تحديد نوع الرسالة
  MessageType _detectMessageType(String message) {
    if (message.contains(RegExp(r'(ما|من|متى|أين|كيف|لماذا|هل|كم|\?)'))) {
      return MessageType.question;
    }
    if (message.contains(RegExp(r'(أضف|احذف|عدل|أنشئ|اعمل|غير)'))) {
      return MessageType.command;
    }
    if (message.contains(RegExp(r'(اعرض|أظهر|اعطني|وضح|ابحث)'))) {
      return MessageType.query;
    }
    if (message.contains(RegExp(r'(مرحبا|أهلا|شكرا|وداعا|كيف حالك)'))) {
      return MessageType.conversation;
    }
    return MessageType.general;
  }

  /// تحديد النية
  String _detectIntent(String message, MessageType type) {
    for (final concept in _educationalConcepts.entries) {
      for (final synonym in concept.value) {
        if (message.contains(synonym)) {
          if (type == MessageType.command) {
            if (message.contains(RegExp(r'(أضف|أنشئ)'))) return 'إضافة_${concept.key}';
            if (message.contains(RegExp(r'(احذف|امسح)'))) return 'حذف_${concept.key}';
            if (message.contains(RegExp(r'(عدل|غير)'))) return 'تعديل_${concept.key}';
          } else if (type == MessageType.query || type == MessageType.question) {
            return 'استعلام_${concept.key}';
          }
        }
      }
    }
    
    return type == MessageType.conversation ? 'محادثة' : 'عام';
  }

  /// استخراج الكيانات
  List<String> _extractEntities(String message) {
    final entities = <String>[];
    
    for (final concept in _educationalConcepts.entries) {
      for (final synonym in concept.value) {
        if (message.contains(synonym)) {
          entities.add(concept.key);
          break;
        }
      }
    }
    
    return entities.toSet().toList();
  }

  /// تقييم مستوى التعقيد
  ComplexityLevel _assessComplexity(String message) {
    int score = 0;
    
    if (message.length > 100) score += 2;
    else if (message.length > 50) score += 1;
    
    final entities = _extractEntities(message);
    score += entities.length;
    
    if (message.contains(RegExp(r'(إحصائية|تقرير|تحليل|مقارنة|معدل)'))) score += 3;
    if (message.contains(RegExp(r'(و|ثم|بعد ذلك|أيضا)'))) score += 2;
    
    if (score >= 6) return ComplexityLevel.high;
    if (score >= 3) return ComplexityLevel.medium;
    return ComplexityLevel.low;
  }

  /// تحديد ما إذا كان يتطلب تفكير
  bool _requiresReasoning(String message, ComplexityLevel complexity) {
    return complexity == ComplexityLevel.high ||
           message.contains(RegExp(r'(لماذا|كيف|اشرح|وضح|قارن|حلل|اقترح)'));
  }

  /// استخراج الكلمات المفتاحية
  List<String> _extractKeywords(String message) {
    final stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك'];
    
    return message.split(' ')
        .where((word) => word.length > 2 && !stopWords.contains(word))
        .toList();
  }

  /// تحليل المشاعر
  String _analyzeSentiment(String message) {
    if (message.contains(RegExp(r'(شكرا|ممتاز|رائع|جيد|أحب)'))) return 'إيجابي';
    if (message.contains(RegExp(r'(مشكلة|خطأ|سيء|لا أحب|صعب)'))) return 'سلبي';
    return 'محايد';
  }

  /// دمج نتائج الفهم
  AIUnderstanding _mergeUnderstanding(
    AIUnderstanding quick,
    Map<String, dynamic> deep,
    String originalMessage,
  ) {
    return AIUnderstanding(
      originalMessage: originalMessage,
      type: _parseMessageType(deep['type']) ?? quick.type,
      intent: deep['intent'] ?? quick.intent,
      entities: List<String>.from(deep['entities'] ?? quick.entities),
      complexity: _parseComplexity(deep['complexity']) ?? quick.complexity,
      requiresReasoning: deep['requires_reasoning'] ?? quick.requiresReasoning,
      confidence: deep['confidence'] ?? quick.confidence,
      keywords: List<String>.from(deep['keywords'] ?? quick.keywords),
      sentiment: deep['sentiment'] ?? quick.sentiment,
      explanation: deep['explanation'],
    );
  }

  /// فهم احتياطي
  AIUnderstanding _fallbackUnderstanding(String message) {
    return AIUnderstanding(
      originalMessage: message,
      type: MessageType.general,
      intent: 'غير محدد',
      entities: [],
      complexity: ComplexityLevel.medium,
      requiresReasoning: false,
      confidence: 0.5,
      keywords: _extractKeywords(message.toLowerCase()),
      sentiment: 'محايد',
    );
  }

  /// تحليل استجابة الفهم
  Map<String, dynamic> _parseUnderstandingResponse(String response) {
    try {
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        return jsonDecode(jsonMatch.group(0)!);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة الفهم: $e');
    }
    return {};
  }

  /// تحليل استجابة التفكير
  AIDecision _parseReasoningResponse(String response, AIUnderstanding understanding) {
    try {
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        final data = jsonDecode(jsonMatch.group(0)!);
        return AIDecision.fromJson(data, understanding.intent);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة التفكير: $e');
    }
    return AIDecision.fallback(understanding.intent);
  }

  /// تحويل نوع الرسالة من النص
  MessageType? _parseMessageType(String? type) {
    if (type == null) return null;
    switch (type.toLowerCase()) {
      case 'سؤال': return MessageType.question;
      case 'طلب': case 'أمر': return MessageType.command;
      case 'استعلام': return MessageType.query;
      case 'محادثة': return MessageType.conversation;
      default: return MessageType.general;
    }
  }

  /// تحويل مستوى التعقيد من النص
  ComplexityLevel? _parseComplexity(String? complexity) {
    if (complexity == null) return null;
    switch (complexity.toLowerCase()) {
      case 'بسيط': return ComplexityLevel.low;
      case 'متوسط': return ComplexityLevel.medium;
      case 'معقد': return ComplexityLevel.high;
      default: return ComplexityLevel.medium;
    }
  }
}

/// فهم الذكاء الاصطناعي للرسالة
class AIUnderstanding {
  final String originalMessage;
  final MessageType type;
  final String intent;
  final List<String> entities;
  final ComplexityLevel complexity;
  final bool requiresReasoning;
  final double confidence;
  final List<String> keywords;
  final String sentiment;
  final String? explanation;

  AIUnderstanding({
    required this.originalMessage,
    required this.type,
    required this.intent,
    required this.entities,
    required this.complexity,
    required this.requiresReasoning,
    required this.confidence,
    required this.keywords,
    required this.sentiment,
    this.explanation,
  });

  Map<String, dynamic> toJson() {
    return {
      'originalMessage': originalMessage,
      'type': type.toString(),
      'intent': intent,
      'entities': entities,
      'complexity': complexity.toString(),
      'requiresReasoning': requiresReasoning,
      'confidence': confidence,
      'keywords': keywords,
      'sentiment': sentiment,
      'explanation': explanation,
    };
  }
}

/// قرار الذكاء الاصطناعي
class AIDecision {
  final String goal;
  final List<String> steps;
  final List<String> requiredData;
  final List<Map<String, dynamic>> actions;
  final String reasoning;
  final String intent;

  AIDecision({
    required this.goal,
    required this.steps,
    required this.requiredData,
    required this.actions,
    required this.reasoning,
    required this.intent,
  });

  factory AIDecision.simple(String intent) {
    return AIDecision(
      goal: 'استجابة بسيطة',
      steps: ['فهم الطلب', 'توليد الاستجابة'],
      requiredData: [],
      actions: [],
      reasoning: 'طلب بسيط لا يتطلب تفكير معقد',
      intent: intent,
    );
  }

  factory AIDecision.fallback(String intent) {
    return AIDecision(
      goal: 'استجابة احتياطية',
      steps: ['توليد استجابة عامة'],
      requiredData: [],
      actions: [],
      reasoning: 'فشل في التحليل، استخدام الاستجابة الاحتياطية',
      intent: intent,
    );
  }

  factory AIDecision.fromJson(Map<String, dynamic> json, String intent) {
    return AIDecision(
      goal: json['goal'] ?? 'غير محدد',
      steps: List<String>.from(json['steps'] ?? []),
      requiredData: List<String>.from(json['required_data'] ?? []),
      actions: List<Map<String, dynamic>>.from(json['actions'] ?? []),
      reasoning: json['reasoning'] ?? 'غير متوفر',
      intent: intent,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'goal': goal,
      'steps': steps,
      'requiredData': requiredData,
      'actions': actions,
      'reasoning': reasoning,
      'intent': intent,
    };
  }
}

/// أنواع الرسائل
enum MessageType {
  question,     // سؤال
  command,      // أمر
  query,        // استعلام
  conversation, // محادثة
  general,      // عام
}

/// مستويات التعقيد
enum ComplexityLevel {
  low,    // بسيط
  medium, // متوسط
  high,   // معقد
}
