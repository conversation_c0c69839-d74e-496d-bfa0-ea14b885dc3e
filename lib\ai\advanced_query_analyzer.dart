import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/security_utils.dart';

/// محلل الاستعلامات المتقدم مع فهم طبيعي للغة
class AdvancedQueryAnalyzer {
  /// نموذج التحليل
  GenerativeModel? _analyzerModel;
  
  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();
  
  /// قاموس المفاهيم والمرادفات
  static final Map<String, List<String>> _conceptDictionary = {
    'إضافة': ['أضف', 'أنشئ', 'اعمل', 'كون', 'سجل', 'أدخل'],
    'حذف': ['احذف', 'امسح', 'أزل', 'ألغ', 'شيل'],
    'تعديل': ['عدل', 'غير', 'حدث', 'طور', 'صحح'],
    'بحث': ['ابحث', 'اعرض', 'أظهر', 'وضح', 'اعطني'],
    'إحصائيات': ['إحصائية', 'تقرير', 'معدل', 'نسبة', 'عدد'],
    'طالب': ['طلاب', 'تلميذ', 'تلاميذ', 'دارس', 'دارسين'],
    'مجموعة': ['مجموعات', 'فصل', 'فصول', 'كلاس', 'صف'],
    'درس': ['دروس', 'حصة', 'حصص', 'محاضرة', 'محاضرات'],
  };

  /// تهيئة المحلل
  Future<void> initialize() async {
    try {
      _analyzerModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.3,
          topK: 20,
          topP: 0.8,
          maxOutputTokens: 1024,
        ),
      );
      debugPrint('✅ تم تهيئة محلل الاستعلامات المتقدم');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محلل الاستعلامات: $e');
    }
  }

  /// تحليل الاستعلام بذكاء متطور
  Future<AdvancedQueryAnalysis> analyzeQuery(
    String query, {
    Map<String, dynamic>? context,
    String? sessionId,
  }) async {
    try {
      // 1. التحليل الأولي السريع
      final quickAnalysis = _performQuickAnalysis(query);
      
      // 2. التحليل العميق بالذكاء الاصطناعي
      final deepAnalysis = await _performDeepAnalysis(query, context);
      
      // 3. دمج النتائج
      return _mergeAnalysisResults(quickAnalysis, deepAnalysis, query);
    } catch (e) {
      debugPrint('❌ خطأ في تحليل الاستعلام: $e');
      return _fallbackAnalysis(query);
    }
  }

  /// التحليل الأولي السريع
  AdvancedQueryAnalysis _performQuickAnalysis(String query) {
    final normalizedQuery = query.toLowerCase().trim();
    
    // تحديد نوع الاستعلام
    QueryType type = _detectQueryType(normalizedQuery);
    
    // تحديد الكيانات
    List<String> entities = _extractEntities(normalizedQuery);
    
    // تحديد النية
    String intent = _detectIntent(normalizedQuery, type);
    
    // تحديد مستوى التعقيد
    QueryComplexity complexity = _assessComplexity(normalizedQuery);
    
    // تحديد ما إذا كان يتطلب تفكير متقدم
    bool requiresAdvancedReasoning = _requiresAdvancedReasoning(normalizedQuery);
    
    // تحديد ما إذا كان متعدد الخطوات
    bool isMultiStep = _isMultiStepQuery(normalizedQuery);
    
    return AdvancedQueryAnalysis(
      originalQuery: query,
      type: type,
      intent: intent,
      entities: entities,
      complexity: complexity,
      requiresAdvancedReasoning: requiresAdvancedReasoning,
      isMultiStep: isMultiStep,
      confidence: 0.7, // سيتم تحديثها بالتحليل العميق
      keywords: _extractKeywords(normalizedQuery),
      sentiment: _analyzeSentiment(normalizedQuery),
    );
  }

  /// التحليل العميق بالذكاء الاصطناعي
  Future<Map<String, dynamic>> _performDeepAnalysis(
    String query,
    Map<String, dynamic>? context,
  ) async {
    if (_analyzerModel == null) return {};

    final prompt = _buildAnalysisPrompt(query, context);
    
    try {
      final content = [Content.text(prompt)];
      final response = await _analyzerModel!.generateContent(content);
      final responseText = response.text ?? '';
      
      return _parseDeepAnalysisResponse(responseText);
    } catch (e) {
      debugPrint('❌ خطأ في التحليل العميق: $e');
      return {};
    }
  }

  /// بناء برومبت التحليل
  String _buildAnalysisPrompt(String query, Map<String, dynamic>? context) {
    return '''
حلل هذا الاستعلام بعمق وأعطني تحليل شامل:

الاستعلام: "$query"

${context != null ? 'السياق: ${jsonEncode(context)}' : ''}

أريد تحليل شامل يتضمن:
1. نوع الاستعلام (سؤال، طلب، أمر، محادثة)
2. النية الحقيقية للمستخدم
3. الكيانات المذكورة (طلاب، مجموعات، دروس، إلخ)
4. مستوى التعقيد (بسيط، متوسط، معقد)
5. هل يتطلب تفكير متقدم؟
6. هل هو متعدد الخطوات؟
7. المشاعر (إيجابي، سلبي، محايد)
8. الكلمات المفتاحية المهمة
9. مستوى الثقة في التحليل

أجب بتنسيق JSON:
{
  "type": "نوع الاستعلام",
  "intent": "النية",
  "entities": ["كيان1", "كيان2"],
  "complexity": "مستوى التعقيد",
  "requires_advanced_reasoning": true/false,
  "is_multi_step": true/false,
  "sentiment": "المشاعر",
  "keywords": ["كلمة1", "كلمة2"],
  "confidence": 0.95,
  "explanation": "شرح التحليل"
}
''';
  }

  /// تحديد نوع الاستعلام
  QueryType _detectQueryType(String query) {
    // كلمات الأسئلة
    if (query.contains(RegExp(r'(ما|من|متى|أين|كيف|لماذا|هل|كم|\?)'))) {
      return QueryType.question;
    }
    
    // كلمات الطلبات
    if (query.contains(RegExp(r'(أضف|احذف|عدل|أنشئ|اعمل|غير)'))) {
      return QueryType.command;
    }
    
    // كلمات البحث
    if (query.contains(RegExp(r'(ابحث|اعرض|أظهر|اعطني|وضح)'))) {
      return QueryType.search;
    }
    
    // محادثة عادية
    if (query.contains(RegExp(r'(مرحبا|أهلا|شكرا|وداعا|كيف حالك)'))) {
      return QueryType.conversation;
    }
    
    return QueryType.general;
  }

  /// استخراج الكيانات
  List<String> _extractEntities(String query) {
    List<String> entities = [];
    
    _conceptDictionary.forEach((concept, synonyms) {
      for (String synonym in synonyms) {
        if (query.contains(synonym)) {
          entities.add(concept);
          break;
        }
      }
    });
    
    return entities.toSet().toList();
  }

  /// تحديد النية
  String _detectIntent(String query, QueryType type) {
    switch (type) {
      case QueryType.command:
        if (query.contains(RegExp(r'(أضف|أنشئ|اعمل)'))) return 'إضافة';
        if (query.contains(RegExp(r'(احذف|امسح|أزل)'))) return 'حذف';
        if (query.contains(RegExp(r'(عدل|غير|حدث)'))) return 'تعديل';
        break;
      case QueryType.question:
      case QueryType.search:
        return 'استعلام';
      case QueryType.conversation:
        return 'محادثة';
      default:
        return 'عام';
    }
    return 'غير محدد';
  }

  /// تقييم مستوى التعقيد
  QueryComplexity _assessComplexity(String query) {
    int complexityScore = 0;
    
    // طول الاستعلام
    if (query.length > 100) complexityScore += 2;
    else if (query.length > 50) complexityScore += 1;
    
    // عدد الكيانات
    final entities = _extractEntities(query);
    complexityScore += entities.length;
    
    // كلمات معقدة
    if (query.contains(RegExp(r'(إحصائية|تقرير|تحليل|مقارنة|معدل)'))) {
      complexityScore += 3;
    }
    
    // عمليات متعددة
    if (query.contains(RegExp(r'(و|ثم|بعد ذلك|أيضا)'))) {
      complexityScore += 2;
    }
    
    if (complexityScore >= 6) return QueryComplexity.high;
    if (complexityScore >= 3) return QueryComplexity.medium;
    return QueryComplexity.low;
  }

  /// تحديد ما إذا كان يتطلب تفكير متقدم
  bool _requiresAdvancedReasoning(String query) {
    return query.contains(RegExp(r'(لماذا|كيف|اشرح|وضح|قارن|حلل|اقترح)'));
  }

  /// تحديد ما إذا كان متعدد الخطوات
  bool _isMultiStepQuery(String query) {
    return query.contains(RegExp(r'(ثم|بعد ذلك|و|أيضا|كذلك)'));
  }

  /// استخراج الكلمات المفتاحية
  List<String> _extractKeywords(String query) {
    // إزالة كلمات الربط والضمائر
    final stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'التي', 'الذي'];
    
    final words = query.split(' ')
        .where((word) => word.length > 2 && !stopWords.contains(word))
        .toList();
    
    return words;
  }

  /// تحليل المشاعر
  String _analyzeSentiment(String query) {
    if (query.contains(RegExp(r'(شكرا|ممتاز|رائع|جيد|أحب)'))) {
      return 'إيجابي';
    }
    if (query.contains(RegExp(r'(مشكلة|خطأ|سيء|لا أحب|صعب)'))) {
      return 'سلبي';
    }
    return 'محايد';
  }

  /// دمج نتائج التحليل
  AdvancedQueryAnalysis _mergeAnalysisResults(
    AdvancedQueryAnalysis quickAnalysis,
    Map<String, dynamic> deepAnalysis,
    String originalQuery,
  ) {
    return AdvancedQueryAnalysis(
      originalQuery: originalQuery,
      type: _parseQueryType(deepAnalysis['type']) ?? quickAnalysis.type,
      intent: deepAnalysis['intent'] ?? quickAnalysis.intent,
      entities: List<String>.from(deepAnalysis['entities'] ?? quickAnalysis.entities),
      complexity: _parseComplexity(deepAnalysis['complexity']) ?? quickAnalysis.complexity,
      requiresAdvancedReasoning: deepAnalysis['requires_advanced_reasoning'] ?? quickAnalysis.requiresAdvancedReasoning,
      isMultiStep: deepAnalysis['is_multi_step'] ?? quickAnalysis.isMultiStep,
      confidence: deepAnalysis['confidence'] ?? quickAnalysis.confidence,
      keywords: List<String>.from(deepAnalysis['keywords'] ?? quickAnalysis.keywords),
      sentiment: deepAnalysis['sentiment'] ?? quickAnalysis.sentiment,
      explanation: deepAnalysis['explanation'],
    );
  }

  /// تحليل احتياطي
  AdvancedQueryAnalysis _fallbackAnalysis(String query) {
    return AdvancedQueryAnalysis(
      originalQuery: query,
      type: QueryType.general,
      intent: 'غير محدد',
      entities: [],
      complexity: QueryComplexity.medium,
      requiresAdvancedReasoning: false,
      isMultiStep: false,
      confidence: 0.5,
      keywords: _extractKeywords(query.toLowerCase()),
      sentiment: 'محايد',
    );
  }

  /// تحليل استجابة التحليل العميق
  Map<String, dynamic> _parseDeepAnalysisResponse(String response) {
    try {
      // البحث عن JSON في الاستجابة
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        return jsonDecode(jsonMatch.group(0)!);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة التحليل العميق: $e');
    }
    return {};
  }

  /// تحويل نوع الاستعلام من النص
  QueryType? _parseQueryType(String? type) {
    if (type == null) return null;
    switch (type.toLowerCase()) {
      case 'سؤال': return QueryType.question;
      case 'طلب': case 'أمر': return QueryType.command;
      case 'بحث': return QueryType.search;
      case 'محادثة': return QueryType.conversation;
      default: return QueryType.general;
    }
  }

  /// تحويل مستوى التعقيد من النص
  QueryComplexity? _parseComplexity(String? complexity) {
    if (complexity == null) return null;
    switch (complexity.toLowerCase()) {
      case 'بسيط': return QueryComplexity.low;
      case 'متوسط': return QueryComplexity.medium;
      case 'معقد': return QueryComplexity.high;
      default: return QueryComplexity.medium;
    }
  }
}

/// تحليل الاستعلام المتقدم
class AdvancedQueryAnalysis {
  final String originalQuery;
  final QueryType type;
  final String intent;
  final List<String> entities;
  final QueryComplexity complexity;
  final bool requiresAdvancedReasoning;
  final bool isMultiStep;
  final double confidence;
  final List<String> keywords;
  final String sentiment;
  final String? explanation;

  AdvancedQueryAnalysis({
    required this.originalQuery,
    required this.type,
    required this.intent,
    required this.entities,
    required this.complexity,
    required this.requiresAdvancedReasoning,
    required this.isMultiStep,
    required this.confidence,
    required this.keywords,
    required this.sentiment,
    this.explanation,
  });

  Map<String, dynamic> toJson() {
    return {
      'originalQuery': originalQuery,
      'type': type.toString(),
      'intent': intent,
      'entities': entities,
      'complexity': complexity.toString(),
      'requiresAdvancedReasoning': requiresAdvancedReasoning,
      'isMultiStep': isMultiStep,
      'confidence': confidence,
      'keywords': keywords,
      'sentiment': sentiment,
      'explanation': explanation,
    };
  }
}

/// أنواع الاستعلامات
enum QueryType {
  question,    // سؤال
  command,     // أمر/طلب
  search,      // بحث
  conversation, // محادثة
  general,     // عام
}

/// مستويات التعقيد
enum QueryComplexity {
  low,    // بسيط
  medium, // متوسط
  high,   // معقد
}
