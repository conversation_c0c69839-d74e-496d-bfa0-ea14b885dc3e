import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'ai_brain.dart';

/// نظام الذاكرة الذكية لـ EduTrack AI
/// يحفظ ويسترجع المعلومات والسياق بذكاء
class AIMemory {
  /// صناديق التخزين
  static Box<Map>? _conversationsBox;
  static Box<Map>? _knowledgeBox;
  static Box<Map>? _userPreferencesBox;
  static Box<Map>? _systemStatsBox;

  /// ذاكرة الجلسة الحالية (في الذاكرة المؤقتة)
  final Map<String, List<MemoryItem>> _sessionMemory = {};

  /// حد أقصى للذاكرة المؤقتة
  static const int _maxSessionItems = 50;
  static const int _maxStoredConversations = 1000;

  /// تهيئة نظام الذاكرة
  Future<void> initialize() async {
    try {
      // فتح صناديق التخزين
      _conversationsBox = await Hive.openBox<Map>('ai_conversations');
      _knowledgeBox = await Hive.openBox<Map>('ai_knowledge');
      _userPreferencesBox = await Hive.openBox<Map>('ai_user_preferences');
      _systemStatsBox = await Hive.openBox<Map>('ai_system_stats');

      // تنظيف الذاكرة القديمة
      await _cleanupOldMemories();

      debugPrint('🧠 تم تهيئة نظام الذاكرة الذكية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام الذاكرة: $e');
      rethrow;
    }
  }

  /// حفظ تفاعل في الذاكرة
  Future<void> saveInteraction(
    String sessionId,
    String userMessage,
    String aiResponse,
    AIUnderstanding understanding,
    AIDecision decision,
  ) async {
    try {
      final interaction = MemoryItem(
        id: _generateId(),
        sessionId: sessionId,
        timestamp: DateTime.now(),
        userMessage: userMessage,
        aiResponse: aiResponse,
        understanding: understanding,
        decision: decision,
        importance: _calculateImportance(understanding, decision),
      );

      // حفظ في ذاكرة الجلسة
      _sessionMemory.putIfAbsent(sessionId, () => []).add(interaction);

      // تنظيف ذاكرة الجلسة إذا امتلأت
      if (_sessionMemory[sessionId]!.length > _maxSessionItems) {
        _sessionMemory[sessionId]!.removeAt(0);
      }

      // حفظ في التخزين الدائم إذا كان مهماً
      if (_isImportant(interaction.importance)) {
        await _saveToStorage(interaction);
      }

      // تحديث المعرفة المستخرجة
      await _updateKnowledge(understanding, decision);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ التفاعل: $e');
    }
  }

  /// استرجاع الذاكرة ذات الصلة
  Future<Map<String, dynamic>> getRelevantMemory(
    String query,
    String sessionId,
  ) async {
    try {
      final relevantMemories = <MemoryItem>[];

      // 1. البحث في ذاكرة الجلسة الحالية
      final sessionMemories = _sessionMemory[sessionId] ?? [];
      relevantMemories.addAll(_findRelevantInSession(query, sessionMemories));

      // 2. البحث في التخزين الدائم
      final storedMemories = await _searchStoredMemories(query);
      relevantMemories.addAll(storedMemories);

      // 3. ترتيب حسب الصلة والأهمية
      relevantMemories.sort((a, b) {
        final scoreA = _calculateRelevanceScore(query, a);
        final scoreB = _calculateRelevanceScore(query, b);
        return scoreB.compareTo(scoreA);
      });

      // 4. أخذ أفضل النتائج
      final topMemories = relevantMemories.take(5).toList();

      return {
        'session_context': _buildSessionContext(sessionMemories),
        'relevant_memories': topMemories.map((m) => m.toJson()).toList(),
        'user_preferences': await _getUserPreferences(),
        'learned_patterns': await _getLearnedPatterns(query),
      };
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع الذاكرة: $e');
      return {};
    }
  }

  /// إنهاء الجلسة وحفظ المعلومات المهمة
  Future<void> endSession(String sessionId) async {
    try {
      final sessionMemories = _sessionMemory[sessionId];
      if (sessionMemories == null || sessionMemories.isEmpty) return;

      // تحليل الجلسة
      final sessionAnalysis = _analyzeSession(sessionMemories);

      // حفظ تحليل الجلسة
      await _conversationsBox!.put(sessionId, {
        'session_id': sessionId,
        'start_time': sessionMemories.first.timestamp.toIso8601String(),
        'end_time': DateTime.now().toIso8601String(),
        'total_interactions': sessionMemories.length,
        'analysis': sessionAnalysis,
        'important_memories': sessionMemories
            .where((m) => _isImportant(m.importance))
            .map((m) => m.toJson())
            .toList(),
      });

      // تحديث تفضيلات المستخدم
      await _updateUserPreferences(sessionMemories);

      // إزالة من الذاكرة المؤقتة
      _sessionMemory.remove(sessionId);

      debugPrint('📝 تم إنهاء الجلسة وحفظ المعلومات: $sessionId');
    } catch (e) {
      debugPrint('❌ خطأ في إنهاء الجلسة: $e');
    }
  }

  /// البحث في ذاكرة الجلسة
  List<MemoryItem> _findRelevantInSession(
    String query,
    List<MemoryItem> memories,
  ) {
    final queryWords = query.toLowerCase().split(' ');

    return memories.where((memory) {
      final messageWords = memory.userMessage.toLowerCase().split(' ');
      final responseWords = memory.aiResponse.toLowerCase().split(' ');

      return queryWords.any(
        (word) =>
            messageWords.contains(word) ||
            responseWords.contains(word) ||
            memory.understanding.keywords.contains(word),
      );
    }).toList();
  }

  /// البحث في التخزين الدائم
  Future<List<MemoryItem>> _searchStoredMemories(String query) async {
    try {
      final results = <MemoryItem>[];
      final queryWords = query.toLowerCase().split(' ');

      // البحث في المحادثات المحفوظة
      for (final key in _conversationsBox!.keys) {
        final conversation = _conversationsBox!.get(key);
        if (conversation == null) continue;

        final importantMemories = conversation['important_memories'] as List?;
        if (importantMemories == null) continue;

        for (final memoryData in importantMemories) {
          final memory = MemoryItem.fromJson(
            Map<String, dynamic>.from(memoryData),
          );

          if (_isRelevantToQuery(memory, queryWords)) {
            results.add(memory);
          }
        }
      }

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في البحث في التخزين: $e');
      return [];
    }
  }

  /// حساب درجة الصلة
  double _calculateRelevanceScore(String query, MemoryItem memory) {
    double score = 0.0;
    final queryWords = query.toLowerCase().split(' ');

    // تطابق الكلمات المفتاحية
    for (final word in queryWords) {
      if (memory.understanding.keywords.contains(word)) score += 2.0;
      if (memory.userMessage.toLowerCase().contains(word)) score += 1.5;
      if (memory.aiResponse.toLowerCase().contains(word)) score += 1.0;
    }

    // تطابق النية
    if (memory.understanding.intent.contains(query.toLowerCase())) score += 3.0;

    // الأهمية
    score += memory.importance.index * 0.5;

    // الحداثة (كلما كان أحدث كلما زادت النقاط)
    final daysSince = DateTime.now().difference(memory.timestamp).inDays;
    score += (30 - daysSince.clamp(0, 30)) * 0.1;

    return score;
  }

  /// بناء سياق الجلسة
  Map<String, dynamic> _buildSessionContext(List<MemoryItem> memories) {
    if (memories.isEmpty) return {};

    final recentMemories = memories.take(5).toList();
    final intents = recentMemories
        .map((m) => m.understanding.intent)
        .toSet()
        .toList();
    final entities = recentMemories
        .expand((m) => m.understanding.entities)
        .toSet()
        .toList();

    return {
      'recent_interactions': recentMemories.length,
      'common_intents': intents,
      'mentioned_entities': entities,
      'session_duration': memories.isNotEmpty
          ? DateTime.now().difference(memories.first.timestamp).inMinutes
          : 0,
      'last_interaction': recentMemories.isNotEmpty
          ? recentMemories.last.toJson()
          : null,
    };
  }

  /// حساب أهمية التفاعل
  ImportanceLevel _calculateImportance(
    AIUnderstanding understanding,
    AIDecision decision,
  ) {
    int score = 0;

    // تعقيد الطلب
    score += understanding.complexity.index;

    // وجود إجراءات
    if (decision.actions.isNotEmpty) score += 2;

    // طول الاستجابة
    if (decision.steps.length > 2) score += 1;

    // نوع الرسالة
    if (understanding.type == MessageType.command) score += 2;
    if (understanding.type == MessageType.question) score += 1;

    // الثقة في الفهم
    if (understanding.confidence > 0.8) score += 1;

    if (score >= 5) return ImportanceLevel.high;
    if (score >= 3) return ImportanceLevel.medium;
    return ImportanceLevel.low;
  }

  /// حفظ في التخزين الدائم
  Future<void> _saveToStorage(MemoryItem item) async {
    try {
      final key = '${item.sessionId}_${item.id}';
      await _conversationsBox!.put(key, item.toJson());
    } catch (e) {
      debugPrint('❌ خطأ في حفظ التخزين الدائم: $e');
    }
  }

  /// تحديث المعرفة المستخرجة
  Future<void> _updateKnowledge(
    AIUnderstanding understanding,
    AIDecision decision,
  ) async {
    try {
      // حفظ أنماط النوايا
      final intentKey = 'intent_${understanding.intent}';
      final intentData = _knowledgeBox!.get(intentKey) ?? {};
      intentData['count'] = (intentData['count'] ?? 0) + 1;
      intentData['last_seen'] = DateTime.now().toIso8601String();
      await _knowledgeBox!.put(intentKey, intentData);

      // حفظ أنماط الكيانات
      for (final entity in understanding.entities) {
        final entityKey = 'entity_$entity';
        final entityData = _knowledgeBox!.get(entityKey) ?? {};
        entityData['count'] = (entityData['count'] ?? 0) + 1;
        entityData['last_seen'] = DateTime.now().toIso8601String();
        await _knowledgeBox!.put(entityKey, entityData);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المعرفة: $e');
    }
  }

  /// تحليل الجلسة
  Map<String, dynamic> _analyzeSession(List<MemoryItem> memories) {
    final intents = memories.map((m) => m.understanding.intent).toList();
    final entities = memories.expand((m) => m.understanding.entities).toList();
    final sentiments = memories.map((m) => m.understanding.sentiment).toList();

    return {
      'total_interactions': memories.length,
      'unique_intents': intents.toSet().length,
      'most_common_intent': _getMostCommon(intents),
      'unique_entities': entities.toSet().length,
      'most_common_entity': _getMostCommon(entities),
      'overall_sentiment': _getMostCommon(sentiments),
      'complexity_distribution': {
        'low': memories
            .where((m) => m.understanding.complexity == ComplexityLevel.low)
            .length,
        'medium': memories
            .where((m) => m.understanding.complexity == ComplexityLevel.medium)
            .length,
        'high': memories
            .where((m) => m.understanding.complexity == ComplexityLevel.high)
            .length,
      },
    };
  }

  /// الحصول على الأكثر شيوعاً
  String _getMostCommon(List<String> items) {
    if (items.isEmpty) return 'غير محدد';

    final counts = <String, int>{};
    for (final item in items) {
      counts[item] = (counts[item] ?? 0) + 1;
    }

    return counts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// تحديث تفضيلات المستخدم
  Future<void> _updateUserPreferences(List<MemoryItem> memories) async {
    try {
      final preferences = _userPreferencesBox!.get('user_preferences') ?? {};

      // تحديث الأنماط المفضلة
      final intents = memories.map((m) => m.understanding.intent).toList();
      for (final intent in intents) {
        final key = 'preferred_$intent';
        preferences[key] = (preferences[key] ?? 0) + 1;
      }

      // تحديث وقت آخر نشاط
      preferences['last_activity'] = DateTime.now().toIso8601String();

      await _userPreferencesBox!.put('user_preferences', preferences);
    } catch (e) {
      debugPrint('❌ خطأ في تحديث التفضيلات: $e');
    }
  }

  /// الحصول على تفضيلات المستخدم
  Future<Map<String, dynamic>> _getUserPreferences() async {
    try {
      return Map<String, dynamic>.from(
        _userPreferencesBox!.get('user_preferences') ?? {},
      );
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع التفضيلات: $e');
      return {};
    }
  }

  /// الحصول على الأنماط المتعلمة
  Future<Map<String, dynamic>> _getLearnedPatterns(String query) async {
    try {
      final patterns = <String, dynamic>{};

      // البحث عن أنماط مشابهة في المعرفة
      for (final key in _knowledgeBox!.keys) {
        final data = _knowledgeBox!.get(key);
        if (data != null && key.toString().contains(query.toLowerCase())) {
          patterns[key] = data;
        }
      }

      return patterns;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع الأنماط: $e');
      return {};
    }
  }

  /// تنظيف الذاكرة القديمة
  Future<void> _cleanupOldMemories() async {
    try {
      // حذف المحادثات القديمة (أكثر من 30 يوم)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final keysToDelete = <String>[];

      for (final key in _conversationsBox!.keys) {
        final conversation = _conversationsBox!.get(key);
        if (conversation != null) {
          final endTime = DateTime.tryParse(conversation['end_time'] ?? '');
          if (endTime != null && endTime.isBefore(cutoffDate)) {
            keysToDelete.add(key);
          }
        }
      }

      for (final key in keysToDelete) {
        await _conversationsBox!.delete(key);
      }

      // الحد من عدد المحادثات المحفوظة
      if (_conversationsBox!.length > _maxStoredConversations) {
        final sortedKeys = _conversationsBox!.keys.toList()..sort();
        final keysToRemove = sortedKeys.take(
          _conversationsBox!.length - _maxStoredConversations,
        );

        for (final key in keysToRemove) {
          await _conversationsBox!.delete(key);
        }
      }

      debugPrint('🧹 تم تنظيف الذاكرة القديمة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الذاكرة: $e');
    }
  }

  /// التحقق من الصلة بالاستعلام
  bool _isRelevantToQuery(MemoryItem memory, List<String> queryWords) {
    final allText =
        '${memory.userMessage} ${memory.aiResponse} ${memory.understanding.keywords.join(' ')}'
            .toLowerCase();

    return queryWords.any((word) => allText.contains(word));
  }

  /// التحقق من أهمية الذكرى
  bool _isImportant(ImportanceLevel importance) {
    return importance == ImportanceLevel.medium ||
        importance == ImportanceLevel.high;
  }

  /// توليد معرف فريد
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// تحرير الموارد
  Future<void> dispose() async {
    try {
      await _conversationsBox?.close();
      await _knowledgeBox?.close();
      await _userPreferencesBox?.close();
      await _systemStatsBox?.close();

      _sessionMemory.clear();

      debugPrint('🔄 تم تحرير موارد الذاكرة');
    } catch (e) {
      debugPrint('❌ خطأ في تحرير الذاكرة: $e');
    }
  }
}

/// عنصر في الذاكرة
class MemoryItem {
  final String id;
  final String sessionId;
  final DateTime timestamp;
  final String userMessage;
  final String aiResponse;
  final AIUnderstanding understanding;
  final AIDecision decision;
  final ImportanceLevel importance;

  MemoryItem({
    required this.id,
    required this.sessionId,
    required this.timestamp,
    required this.userMessage,
    required this.aiResponse,
    required this.understanding,
    required this.decision,
    required this.importance,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sessionId': sessionId,
      'timestamp': timestamp.toIso8601String(),
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'understanding': understanding.toJson(),
      'decision': decision.toJson(),
      'importance': importance.toString(),
    };
  }

  factory MemoryItem.fromJson(Map<String, dynamic> json) {
    return MemoryItem(
      id: json['id'],
      sessionId: json['sessionId'],
      timestamp: DateTime.parse(json['timestamp']),
      userMessage: json['userMessage'],
      aiResponse: json['aiResponse'],
      understanding: AIUnderstanding(
        originalMessage: json['understanding']['originalMessage'],
        type: MessageType.values.firstWhere(
          (e) => e.toString() == json['understanding']['type'],
          orElse: () => MessageType.general,
        ),
        intent: json['understanding']['intent'],
        entities: List<String>.from(json['understanding']['entities']),
        complexity: ComplexityLevel.values.firstWhere(
          (e) => e.toString() == json['understanding']['complexity'],
          orElse: () => ComplexityLevel.medium,
        ),
        requiresReasoning: json['understanding']['requiresReasoning'],
        confidence: json['understanding']['confidence'],
        keywords: List<String>.from(json['understanding']['keywords']),
        sentiment: json['understanding']['sentiment'],
        explanation: json['understanding']['explanation'],
      ),
      decision: AIDecision.fromJson(
        Map<String, dynamic>.from(json['decision']),
        json['understanding']['intent'],
      ),
      importance: ImportanceLevel.values.firstWhere(
        (e) => e.toString() == json['importance'],
        orElse: () => ImportanceLevel.low,
      ),
    );
  }
}

/// مستويات الأهمية
enum ImportanceLevel {
  low, // منخفض
  medium, // متوسط
  high, // عالي
}
