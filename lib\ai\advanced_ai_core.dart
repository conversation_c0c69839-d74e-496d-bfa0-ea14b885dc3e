import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../providers/app_provider.dart';
import '../utils/security_utils.dart';
import 'advanced_query_analyzer.dart';
import 'smart_memory_system.dart';
import 'advanced_security_system.dart';
import 'multimodal_processor.dart';
import 'intelligent_response_generator.dart';

/// النواة المتطورة للذكاء الاصطناعي
/// نظام ذكي شامل مع قدرات متقدمة
class SmartAICore {
  /// نموذج الذكاء الاصطناعي الرئيسي
  static GenerativeModel? _primaryModel;

  /// نموذج احتياطي للمهام المعقدة
  static GenerativeModel? _advancedModel;

  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();

  /// حالة التهيئة
  static bool _isInitialized = false;

  /// نظام الأمان المتقدم
  static late AdvancedSecuritySystem _securitySystem;

  /// نظام الذاكرة الذكي
  static late SmartMemorySystem _memorySystem;

  /// محلل الاستعلامات المتقدم
  static late QueryAnalyzer _queryAnalyzer;

  /// معالج متعدد الوسائط
  static late MultimodalProcessor _multimodalProcessor;

  /// مولد الاستجابات الذكي
  static late IntelligentResponseGenerator _responseGenerator;

  /// معرف الجلسة الحالية
  static String? _currentSessionId;

  /// إحصائيات الأداء
  static final Map<String, dynamic> _performanceStats = {};

  /// تهيئة النظام المتطور
  static Future<bool> initialize() async {
    try {
      debugPrint('🚀 بدء تهيئة النظام المتطور للذكاء الاصطناعي...');

      // 1. تهيئة نظام الأمان المتقدم
      _securitySystem = AdvancedSecuritySystem();
      final securityCheck = await _securitySystem.performSecurityCheck();

      if (!securityCheck.isSecure) {
        debugPrint('❌ فشل فحص الأمان: ${securityCheck.reason}');
        return false;
      }

      // 2. تهيئة النماذج
      await _initializeModels();

      // 3. تهيئة نظام الذاكرة
      _memorySystem = SmartMemorySystem();
      await _memorySystem.initialize();

      // 4. تهيئة محلل الاستعلامات المتقدم
      _queryAnalyzer = QueryAnalyzer();
      await _queryAnalyzer.initialize();

      // 5. تهيئة معالج متعدد الوسائط
      _multimodalProcessor = MultimodalProcessor();
      await _multimodalProcessor.initialize();

      // 6. تهيئة مولد الاستجابات الذكي
      _responseGenerator = IntelligentResponseGenerator();
      await _responseGenerator.initialize();

      // 7. إنشاء جلسة جديدة
      _currentSessionId = _generateSessionId();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة النظام المتطور بنجاح');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة النظام المتطور: $e');
      return false;
    }
  }

  /// تهيئة النماذج
  static Future<void> _initializeModels() async {
    // النموذج الرئيسي للمهام العادية
    _primaryModel = GenerativeModel(
      model: 'gemini-1.5-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 4096,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
      ],
    );

    // النموذج المتقدم للمهام المعقدة
    _advancedModel = GenerativeModel(
      model: 'gemini-1.5-pro',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        temperature: 0.8,
        topK: 50,
        topP: 0.98,
        maxOutputTokens: 8192,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
      ],
    );
  }

  /// معالجة رسالة المستخدم بذكاء متطور
  static Future<AIResponse> processMessage(
    String message,
    AppProvider provider, {
    List<File>? attachments,
    Map<String, dynamic>? context,
  }) async {
    if (!_isInitialized) {
      throw Exception('النظام غير مهيأ. يرجى استدعاء initialize() أولاً');
    }

    try {
      final startTime = DateTime.now();

      // 1. فحص الأمان
      final securityResult = await _securitySystem.validateInput(
        message,
        attachments,
      );
      if (!securityResult.isValid) {
        return AIResponse.error(
          'تم رفض الطلب لأسباب أمنية: ${securityResult.reason}',
        );
      }

      // 2. تحليل الاستعلام المتقدم
      final queryAnalysis = await _queryAnalyzer.analyzeQuery(
        message,
        context: context,
        sessionId: _currentSessionId!,
      );

      // 3. استرجاع السياق من الذاكرة
      final memoryContext = await _memorySystem.getRelevantContext(
        message,
        _currentSessionId!,
      );

      // 4. معالجة الوسائط المتعددة
      Map<String, dynamic>? mediaAnalysis;
      if (attachments != null && attachments.isNotEmpty) {
        mediaAnalysis = await _multimodalProcessor.processAttachments(
          attachments,
        );
      }

      // 5. بناء السياق الشامل
      final comprehensiveContext = _buildComprehensiveContext(
        provider,
        queryAnalysis,
        memoryContext,
        mediaAnalysis,
        context,
      );

      // 6. اختيار النموذج المناسب
      final model = _selectAppropriateModel(queryAnalysis);

      // 7. توليد الاستجابة
      final response = await _responseGenerator.generateResponse(
        message,
        queryAnalysis,
        comprehensiveContext,
        model,
      );

      // 8. حفظ في الذاكرة
      await _memorySystem.saveInteraction(
        _currentSessionId!,
        message,
        response.message,
        queryAnalysis,
        comprehensiveContext,
      );

      // 9. تحديث الإحصائيات
      _updatePerformanceStats(startTime, queryAnalysis);

      return response;
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الرسالة: $e');
      return AIResponse.error('عذراً، حدث خطأ في معالجة طلبك');
    }
  }

  /// بناء السياق الشامل
  static Map<String, dynamic> _buildComprehensiveContext(
    AppProvider provider,
    AdvancedQueryAnalysis queryAnalysis,
    Map<String, dynamic> memoryContext,
    Map<String, dynamic>? mediaAnalysis,
    Map<String, dynamic>? additionalContext,
  ) {
    return {
      'app_data': {
        'groups': provider.groups
            .map(
              (g) => {
                'id': g.id,
                'name': g.name,
                'subject': g.subject,
                'studentIds': g.studentIds,
                'monthlyFee': g.monthlyFee,
                'createdAt': g.createdAt.toIso8601String(),
              },
            )
            .toList(),
        'students': provider.students
            .map(
              (s) => {
                'id': s.id,
                'name': s.name,
                'groupId': s.groupId,
                'isPresent': s.isPresent,
                'hasPaid': s.hasPaid,
                'monthlyPayment': s.monthlyPayment,
                'lastAttendance': s.lastAttendance.toIso8601String(),
              },
            )
            .toList(),
        'lessons': provider.lessons
            .map(
              (l) => {
                'id': l.id,
                'groupId': l.groupId,
                'dateTime': l.dateTime.toIso8601String(),
                'isCompleted': l.isCompleted,
                'attendedStudentIds': l.attendedStudentIds,
                'notes': l.notes,
              },
            )
            .toList(),
        'theme_mode': provider.isDarkMode ? 'dark' : 'light',
      },
      'query_analysis': queryAnalysis.toJson(),
      'memory_context': memoryContext,
      'media_analysis': mediaAnalysis,
      'additional_context': additionalContext ?? {},
      'session_id': _currentSessionId,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// اختيار النموذج المناسب
  static GenerativeModel _selectAppropriateModel(
    AdvancedQueryAnalysis analysis,
  ) {
    // استخدام النموذج المتقدم للمهام المعقدة
    if (analysis.complexity == QueryComplexity.high ||
        analysis.requiresAdvancedReasoning ||
        analysis.isMultiStep) {
      return _advancedModel!;
    }

    return _primaryModel!;
  }

  /// توليد معرف جلسة فريد
  static String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// تحديث إحصائيات الأداء
  static void _updatePerformanceStats(
    DateTime startTime,
    AdvancedQueryAnalysis analysis,
  ) {
    final duration = DateTime.now().difference(startTime);

    _performanceStats['total_requests'] =
        (_performanceStats['total_requests'] ?? 0) + 1;
    _performanceStats['average_response_time'] =
        ((_performanceStats['average_response_time'] ?? 0) +
            duration.inMilliseconds) /
        2;
    _performanceStats['last_request_time'] = DateTime.now().toIso8601String();

    // إحصائيات حسب نوع الاستعلام
    final queryType = analysis.type.toString();
    _performanceStats['by_type'] ??= <String, int>{};
    _performanceStats['by_type'][queryType] =
        (_performanceStats['by_type'][queryType] ?? 0) + 1;
  }

  /// الحصول على إحصائيات الأداء
  static Map<String, dynamic> getPerformanceStats() {
    return Map.from(_performanceStats);
  }

  /// إنهاء الجلسة الحالية
  static Future<void> endCurrentSession() async {
    if (_currentSessionId != null) {
      await _memorySystem.endSession(_currentSessionId!);
      _currentSessionId = null;
    }
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    await endCurrentSession();
    await _memorySystem.dispose();
    _isInitialized = false;
    debugPrint('🧹 تم تنظيف موارد النظام المتطور');
  }

  /// التحقق من حالة النظام
  static bool get isInitialized => _isInitialized;

  /// الحصول على معرف الجلسة الحالية
  static String? get currentSessionId => _currentSessionId;
}

/// استجابة النظام المتطور
class AIResponse {
  final String message;
  final bool isSuccess;
  final bool hasAction;
  final Map<String, dynamic>? actionData;
  final Map<String, dynamic>? metadata;
  final String? error;

  AIResponse({
    required this.message,
    this.isSuccess = true,
    this.hasAction = false,
    this.actionData,
    this.metadata,
    this.error,
  });

  factory AIResponse.success(
    String message, {
    bool hasAction = false,
    Map<String, dynamic>? actionData,
    Map<String, dynamic>? metadata,
  }) {
    return AIResponse(
      message: message,
      isSuccess: true,
      hasAction: hasAction,
      actionData: actionData,
      metadata: metadata,
    );
  }

  factory AIResponse.error(String error) {
    return AIResponse(message: error, isSuccess: false, error: error);
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'isSuccess': isSuccess,
      'hasAction': hasAction,
      'actionData': actionData,
      'metadata': metadata,
      'error': error,
    };
  }
}
