import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../ai/edutrack_ai_core.dart';
import '../providers/app_provider.dart';
import '../screens/edutrack_ai_screen.dart';
import '../theme/app_theme.dart';

/// Widget الذكاء الاصطناعي الجديد لـ EduTrack
/// يعرض معاينة سريعة ويوفر وصول سهل للمساعد الذكي
class EduTrackAIWidget extends StatefulWidget {
  const EduTrackAIWidget({super.key});

  @override
  State<EduTrackAIWidget> createState() => _EduTrackAIWidgetState();
}

class _EduTrackAIWidgetState extends State<EduTrackAIWidget>
    with TickerProviderStateMixin {
  /// متحكمات الرسوم المتحركة
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shimmerAnimation;

  /// حالة النظام
  bool _isInitialized = false;
  bool _isLoading = false;
  String _quickResponse = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkAIStatus();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  /// فحص حالة الذكاء الاصطناعي
  Future<void> _checkAIStatus() async {
    setState(() => _isLoading = true);

    try {
      _isInitialized = EduTrackAI.isInitialized;
      
      if (!_isInitialized) {
        _isInitialized = await EduTrackAI.initialize();
      }

      if (_isInitialized) {
        await _generateQuickInsight();
      }
    } catch (e) {
      debugPrint('خطأ في فحص حالة الذكاء الاصطناعي: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// توليد نظرة سريعة ذكية
  Future<void> _generateQuickInsight() async {
    try {
      final provider = Provider.of<AppProvider>(context, listen: false);
      
      // إنشاء رسالة تلقائية للحصول على نظرة عامة
      final response = await EduTrackAI.processMessage(
        'أعطني نظرة سريعة على الوضع الحالي',
        provider,
      );

      if (response.isSuccess) {
        setState(() {
          _quickResponse = _extractQuickInsight(response.message);
        });
      }
    } catch (e) {
      debugPrint('خطأ في توليد النظرة السريعة: $e');
    }
  }

  /// استخراج النظرة السريعة من الاستجابة
  String _extractQuickInsight(String fullResponse) {
    // استخراج أول جملتين أو 100 حرف
    final sentences = fullResponse.split('.');
    if (sentences.length >= 2) {
      return '${sentences[0]}.${sentences[1]}.';
    }
    
    if (fullResponse.length > 100) {
      return '${fullResponse.substring(0, 97)}...';
    }
    
    return fullResponse;
  }

  /// إرسال رسالة سريعة
  Future<void> _sendQuickMessage(String message) async {
    if (!_isInitialized) return;

    setState(() => _isLoading = true);
    _shimmerController.repeat();

    try {
      final provider = Provider.of<AppProvider>(context, listen: false);
      final response = await EduTrackAI.processMessage(message, provider);

      if (response.isSuccess) {
        setState(() {
          _quickResponse = _extractQuickInsight(response.message);
        });
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة السريعة: $e');
    } finally {
      setState(() => _isLoading = false);
      _shimmerController.stop();
      _shimmerController.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primary.withValues(alpha: 0.1),
            AppTheme.secondary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (_isInitialized) ...[
            _buildQuickInsight(),
            _buildQuickActions(),
          ] else
            _buildInitializationStatus(),
        ],
      ),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.smart_toy_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'EduTrack AI',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _isInitialized ? 'مساعدك الذكي الجديد' : 'جاري التهيئة...',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _isInitialized
                ? () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EduTrackAIScreen(),
                      ),
                    )
                : null,
            icon: const Icon(
              Icons.chat_rounded,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء النظرة السريعة
  Widget _buildQuickInsight() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.insights_rounded,
                color: AppTheme.primary,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'نظرة سريعة',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _isLoading
              ? _buildShimmerText()
              : Text(
                  _quickResponse.isEmpty
                      ? 'اضغط على إحدى الأزرار أدناه للحصول على معلومات سريعة'
                      : _quickResponse,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                    height: 1.4,
                  ),
                ),
        ],
      ),
    );
  }

  /// بناء نص متحرك للتحميل
  Widget _buildShimmerText() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
                Colors.grey[300]!,
              ],
              stops: [
                _shimmerAnimation.value - 0.3,
                _shimmerAnimation.value,
                _shimmerAnimation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickActionButton(
                'عدد الطلاب',
                Icons.people_rounded,
                () => _sendQuickMessage('كم عدد الطلاب الإجمالي؟'),
              ),
              _buildQuickActionButton(
                'الحضور اليوم',
                Icons.today_rounded,
                () => _sendQuickMessage('كم عدد الطلاب الحاضرين اليوم؟'),
              ),
              _buildQuickActionButton(
                'المدفوعات',
                Icons.payment_rounded,
                () => _sendQuickMessage('ما هو وضع المدفوعات؟'),
              ),
              _buildQuickActionButton(
                'الدروس',
                Icons.book_rounded,
                () => _sendQuickMessage('كم درس مكتمل اليوم؟'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: _isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: AppTheme.primary,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: AppTheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة التهيئة
  Widget _buildInitializationStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'جاري تهيئة النظام الذكي...',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى الانتظار قليلاً',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
