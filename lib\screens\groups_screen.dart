import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../widgets/simple_background.dart';
import '../widgets/simple_bottom_nav.dart';
import '../theme/simple_theme.dart';
import '../models/group.dart';
import '../models/student.dart';

class GroupsScreen extends StatefulWidget {
  const GroupsScreen({super.key});

  @override
  State<GroupsScreen> createState() => _GroupsScreenState();
}

class _GroupsScreenState extends State<GroupsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _searchQuery = '';
  String _selectedSubject = 'الكل';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                _buildHeader(context, provider),
                const SizedBox(height: 24),

                // Stats Cards
                _buildStatsCards(provider),
                const SizedBox(height: 24),

                // Search and Filter Section
                _buildSearchAndFilter(provider),
                const SizedBox(height: 20),

                // Groups List
                _buildGroupsList(provider),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء الهيدر
  Widget _buildHeader(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              SimpleTheme.primaryBlue.withValues(alpha: 0.1),
              SimpleTheme.accentPink.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المجموعات',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة وتنظيم مجموعات الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
            Container(
              decoration: BoxDecoration(
                gradient: SimpleTheme.primaryGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: SimpleTheme.primaryBlue.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () => _showAddGroupDialog(context, provider),
                icon: const Icon(
                  Icons.add_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                tooltip: 'إضافة مجموعة جديدة',
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقات الإحصائيات
  Widget _buildStatsCards(AppProvider provider) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي المجموعات',
            provider.groups.length.toString(),
            Icons.groups,
            SimpleTheme.primaryBlue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'إجمالي الطلاب',
            provider.totalStudents.toString(),
            Icons.person,
            SimpleTheme.primaryBlue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'المواد المختلفة',
            provider.groups.map((g) => g.subject).toSet().length.toString(),
            Icons.book,
            SimpleTheme.accentPink,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  // بناء البحث والفلترة
  Widget _buildSearchAndFilter(AppProvider provider) {
    final subjects = ['الكل', ...provider.groups.map((g) => g.subject).toSet()];

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(
            decoration: BoxDecoration(
              color: SimpleTheme.surfaceBg.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: TextField(
              style: GoogleFonts.cairo(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'البحث في المجموعات...',
                hintStyle: GoogleFonts.cairo(
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: SimpleTheme.surfaceBg.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedSubject,
                dropdownColor: SimpleTheme.surfaceBg,
                style: GoogleFonts.cairo(color: Colors.white),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
                items: subjects.map((subject) {
                  return DropdownMenuItem(value: subject, child: Text(subject));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedSubject = value!;
                  });
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  // بناء قائمة المجموعات
  Widget _buildGroupsList(AppProvider provider) {
    var filteredGroups = provider.groups.where((group) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          group.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          group.subject.toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesSubject =
          _selectedSubject == 'الكل' || group.subject == _selectedSubject;

      return matchesSearch && matchesSubject;
    }).toList();

    if (filteredGroups.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: filteredGroups.map((group) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _GroupCard(group: group),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_add,
            size: 64,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مجموعات بعد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على + لإضافة مجموعة جديدة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddGroupDialog(BuildContext context, AppProvider provider) {
    final nameController = TextEditingController();
    final subjectController = TextEditingController();
    final feeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                SimpleTheme.surfaceBg,
                SimpleTheme.surfaceBg.withValues(alpha: 0.9),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'إضافة مجموعة جديدة',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildDialogTextField(
                controller: nameController,
                label: 'اسم المجموعة',
                icon: Icons.group,
                hint: 'مثال: مجموعة الرياضيات المتقدمة',
              ),
              const SizedBox(height: 16),
              _buildDialogTextField(
                controller: subjectController,
                label: 'المادة الدراسية',
                icon: Icons.book,
                hint: 'مثال: الرياضيات',
              ),
              const SizedBox(height: 16),
              _buildDialogTextField(
                controller: feeController,
                label: 'الرسوم الشهرية',
                icon: Icons.attach_money,
                hint: 'مثال: 500',
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: _buildDialogButton(
                      text: 'إلغاء',
                      onPressed: () => Navigator.pop(context),
                      isSecondary: true,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDialogButton(
                      text: 'إضافة',
                      onPressed: () => _addGroup(
                        context,
                        provider,
                        nameController.text,
                        subjectController.text,
                        feeController.text,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDialogTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    TextInputType? keyboardType,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: SimpleTheme.surfaceBg.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        style: GoogleFonts.cairo(color: Colors.white),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.7),
          ),
          hintStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.5),
          ),
          prefixIcon: Icon(icon, color: SimpleTheme.primaryBlue),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildDialogButton({
    required String text,
    required VoidCallback onPressed,
    bool isSecondary = false,
  }) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        gradient: isSecondary ? null : SimpleTheme.primaryGradient,
        color: isSecondary ? Colors.white.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSecondary
              ? Colors.white.withValues(alpha: 0.2)
              : Colors.transparent,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Center(
            child: Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _addGroup(
    BuildContext context,
    AppProvider provider,
    String name,
    String subject,
    String feeText,
  ) {
    if (name.isEmpty || subject.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى ملء جميع الحقول المطلوبة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final fee = double.tryParse(feeText) ?? 0.0;

    final group = Group(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      subject: subject,
      monthlyFee: fee,
    );

    provider.addGroup(group);

    Navigator.pop(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة المجموعة بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: SimpleTheme.primaryBlue,
      ),
    );
  }
}

class _GroupCard extends StatefulWidget {
  final Group group;

  const _GroupCard({required this.group});

  @override
  State<_GroupCard> createState() => _GroupCardState();
}

class _GroupCardState extends State<_GroupCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final students = provider.getStudentsByGroup(widget.group.id);

        return PremiumCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () => setState(() => isExpanded = !isExpanded),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  widget.group.name,
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      backgroundColor: const Color(0xFF1e293b),
                                      title: Text(
                                        'حذف المجموعة',
                                        style: GoogleFonts.cairo(
                                          color: Colors.white,
                                        ),
                                      ),
                                      content: Text(
                                        'هل أنت متأكد من حذف هذه المجموعة؟ سيتم حذف جميع الطلاب المرتبطين بها.',
                                        style: GoogleFonts.cairo(
                                          color: Colors.white70,
                                        ),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: Text(
                                            'إلغاء',
                                            style: GoogleFonts.cairo(
                                              color: Colors.white70,
                                            ),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            provider.deleteGroup(
                                              widget.group.id,
                                            );
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            'حذف',
                                            style: GoogleFonts.cairo(
                                              color: Colors.red,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                  size: 20,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.group.subject,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              _buildInfoChip(
                                '${students.length} طالب',
                                Icons.person,
                              ),
                              const SizedBox(width: 8),
                              _buildInfoChip(
                                '${widget.group.monthlyFee} ر.س',
                                Icons.attach_money,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
              if (isExpanded) ...[
                const SizedBox(height: 16),
                const Divider(color: Colors.white30),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الطلاب',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    IconButton(
                      onPressed: () => _showAddStudentDialog(context, provider),
                      icon: const Icon(Icons.add, color: Color(0xFF6366f1)),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...students.map(
                  (student) => _buildStudentTile(student, provider),
                ),
                if (students.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: Text(
                        'لا يوجد طلاب في هذه المجموعة',
                        style: GoogleFonts.cairo(
                          color: Colors.white60,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.white70),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(fontSize: 12, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentTile(Student student, AppProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                student.name,
                style: GoogleFonts.cairo(color: Colors.white, fontSize: 14),
              ),
            ),
            Text(
              '${student.monthlyPayment} ر.س',
              style: GoogleFonts.cairo(color: Colors.white70, fontSize: 12),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                provider.deleteStudent(student.id);
              },
              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddStudentDialog(BuildContext context, AppProvider provider) {
    final nameController = TextEditingController();
    final paymentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1e293b),
        title: Text(
          'إضافة طالب جديد',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              style: GoogleFonts.cairo(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'اسم الطالب',
                labelStyle: GoogleFonts.cairo(color: Colors.white70),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white30),
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Color(0xFF6366f1)),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: paymentController
                ..text = widget.group.monthlyFee.toString(),
              enabled: false,
              keyboardType: TextInputType.number,
              style: GoogleFonts.cairo(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'الرسوم الشهرية (من المجموعة)',
                labelStyle: GoogleFonts.cairo(color: Colors.white70),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white30),
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Color(0xFF6366f1)),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                final student = Student(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  name: nameController.text,
                  groupId: widget.group.id,
                  monthlyPayment: widget.group.monthlyFee,
                );
                provider.addStudent(student);
                Navigator.pop(context);
              }
            },
            child: Text(
              'إضافة',
              style: GoogleFonts.cairo(color: const Color(0xFF6366f1)),
            ),
          ),
        ],
      ),
    );
  }
}
