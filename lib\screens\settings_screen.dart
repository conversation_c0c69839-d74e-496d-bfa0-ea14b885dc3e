import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';

import '../services/backup_service.dart';
import '../services/data_service.dart';
import 'backup_manager_screen.dart';
import 'check_updates_screen.dart';
import 'privacy_policy_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 24),

                // Settings Content
                _buildAppSettings(context, provider),
                const SizedBox(height: 20),
                _buildDataSettings(context, provider),
                const SizedBox(height: 20),
                _buildBackupSettings(context, provider),
                const SizedBox(height: 20),
                _buildAboutSettings(context),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF6366f1).withValues(alpha: 0.1),
            const Color(0xFFec4899).withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366f1), Color(0xFFec4899)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.settings, color: Colors.white, size: 32),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الإعدادات',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة إعدادات التطبيق والبيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings(BuildContext context, AppProvider provider) {
    return _buildSettingsSection(
      title: 'إعدادات التطبيق',
      icon: Icons.app_settings_alt,
      children: [
        _buildSettingsTile(
          title: provider.isDarkMode ? 'المظهر الداكن' : 'المظهر الفاتح',
          subtitle: provider.isDarkMode
              ? 'تفعيل المظهر الداكن للتطبيق'
              : 'تفعيل المظهر الفاتح للتطبيق',
          icon: provider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
          trailing: Switch(
            value: provider.isDarkMode,
            onChanged: (value) async {
              await provider.toggleTheme();
            },
            activeColor: const Color(0xFF6366f1),
          ),
        ),
      ],
    );
  }

  Widget _buildDataSettings(BuildContext context, AppProvider provider) {
    return _buildSettingsSection(
      title: 'إدارة البيانات',
      icon: Icons.storage,
      children: [
        _buildSettingsTile(
          title: 'إحصائيات البيانات',
          subtitle:
              '${provider.totalStudents} طالب، ${provider.groups.length} مجموعة',
          icon: Icons.analytics,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Colors.white54,
            size: 16,
          ),
          onTap: () {
            _showDataStatsDialog(context, provider);
          },
        ),
        _buildSettingsTile(
          title: 'مسح جميع البيانات',
          subtitle: 'حذف جميع الطلاب والمجموعات',
          icon: Icons.delete_forever,
          iconColor: Colors.red,
          onTap: () {
            _showClearDataDialog(context, provider);
          },
        ),
        _buildSettingsTile(
          title: 'إعادة تعيين التطبيق',
          subtitle: 'إعادة التطبيق لحالته الأولى',
          icon: Icons.refresh,
          iconColor: Colors.orange,
          onTap: () {
            _showResetAppDialog(context, provider);
          },
        ),
      ],
    );
  }

  Widget _buildBackupSettings(BuildContext context, AppProvider provider) {
    return _buildSettingsSection(
      title: 'النسخ الاحتياطي',
      icon: Icons.backup,
      children: [
        _buildSettingsTile(
          title: 'إدارة النسخ الاحتياطية',
          subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
          icon: Icons.cloud_upload,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Colors.white54,
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const BackupManagerScreen(),
              ),
            );
          },
        ),
        _buildSettingsTile(
          title: 'نسخة احتياطية سريعة',
          subtitle: 'إنشاء نسخة احتياطية فورية',
          icon: Icons.save,
          onTap: () async {
            await _createQuickBackup(context, provider);
          },
        ),
        _buildSettingsTile(
          title: 'النسخ التلقائي',
          subtitle: 'نسخ احتياطي تلقائي يومي',
          icon: Icons.schedule,
          trailing: Switch(
            value: false, // يمكن ربطه بإعداد
            onChanged: (value) {
              // تنفيذ تفعيل النسخ التلقائي
            },
            activeColor: const Color(0xFF6366f1),
          ),
        ),
      ],
    );
  }

  Widget _buildAboutSettings(BuildContext context) {
    return _buildSettingsSection(
      title: 'حول التطبيق',
      icon: Icons.info,
      children: [
        _buildSettingsTile(
          title: 'إصدار التطبيق',
          subtitle: '1.0.0',
          icon: Icons.app_registration,
        ),
        _buildSettingsTile(
          title: 'فحص التحديثات',
          subtitle: 'التحقق من وجود إصدار جديد',
          icon: Icons.system_update,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Colors.white54,
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CheckUpdatesScreen(),
              ),
            );
          },
        ),
        _buildSettingsTile(
          title: 'سياسة الخصوصية',
          subtitle: 'كيفية حماية بياناتك',
          icon: Icons.privacy_tip,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Colors.white54,
            size: 16,
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PrivacyPolicyScreen(),
              ),
            );
          },
        ),
        _buildSettingsTile(
          title: 'المطور',
          subtitle: 'Mohamed Youssef',
          icon: Icons.code,
        ),
      ],
    );
  }

  // دالة بناء قسم الإعدادات
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.05),
            Colors.white.withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6366f1).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF6366f1), size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  // دالة بناء عنصر الإعدادات
  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20, bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.05),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? const Color(0xFF6366f1)).withValues(
              alpha: 0.1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor ?? const Color(0xFF6366f1),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }

  // عرض حوار إحصائيات البيانات
  void _showDataStatsDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1e293b),
                const Color(0xFF1e293b).withValues(alpha: 0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إحصائيات البيانات',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 20),
              _buildStatRow('إجمالي الطلاب', provider.totalStudents.toString()),
              _buildStatRow(
                'إجمالي المجموعات',
                provider.groups.length.toString(),
              ),
              _buildStatRow(
                'إجمالي الدروس',
                provider.lessons.length.toString(),
              ),
              _buildStatRow(
                'المواد المختلفة',
                provider.groups.map((g) => g.subject).toSet().length.toString(),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366f1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'إغلاق',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF6366f1),
            ),
          ),
        ],
      ),
    );
  }

  // عرض حوار مسح البيانات
  void _showClearDataDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1e293b),
        title: Text(
          'تأكيد مسح البيانات',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              // مسح جميع البيانات
              await DataService.students.clear();
              await DataService.groups.clear();
              await DataService.lessons.clear();
              provider.loadData();
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم مسح جميع البيانات بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('مسح', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // عرض حوار إعادة تعيين التطبيق
  void _showResetAppDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1e293b),
        title: Text(
          'إعادة تعيين التطبيق',
          style: GoogleFonts.cairo(color: Colors.white),
        ),
        content: Text(
          'سيتم إعادة التطبيق لحالته الأولى مع مسح جميع البيانات والإعدادات.',
          style: GoogleFonts.cairo(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              // مسح جميع البيانات
              await DataService.students.clear();
              await DataService.groups.clear();
              await DataService.lessons.clear();
              provider.loadData();
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم إعادة تعيين التطبيق بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }

  // إنشاء نسخة احتياطية سريعة
  Future<void> _createQuickBackup(
    BuildContext context,
    AppProvider provider,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      await BackupService.createBackup();

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            'تم إنشاء النسخة الاحتياطية بنجاح',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            'فشل في إنشاء النسخة الاحتياطية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
