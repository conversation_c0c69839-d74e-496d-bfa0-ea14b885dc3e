import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../providers/app_provider.dart';
import '../utils/security_utils.dart';
import 'ai_brain.dart';
import 'ai_memory.dart';
import 'ai_security.dart';
import 'ai_processor.dart';
import 'ai_response_generator.dart';

/// النواة الذكية لنظام EduTrack AI
/// نظام ذكاء اصطناعي متطور مصمم خصيصاً للتعليم
class EduTrackAI {
  /// حالة النظام
  static bool _isInitialized = false;
  static bool _isProcessing = false;
  
  /// مكونات النظام الذكي
  static late AIBrain _brain;
  static late AIMemory _memory;
  static late AISecurity _security;
  static late AIProcessor _processor;
  static late AIResponseGenerator _responseGenerator;
  
  /// معرف الجلسة الحالية
  static String? _currentSessionId;
  
  /// إحصائيات النظام
  static final Map<String, dynamic> _stats = {
    'total_interactions': 0,
    'successful_responses': 0,
    'average_response_time': 0.0,
    'last_interaction': null,
    'system_uptime': DateTime.now(),
  };

  /// تهيئة نظام EduTrack AI
  static Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      debugPrint('🚀 بدء تهيئة EduTrack AI...');
      
      // 1. تهيئة الحارس الأمني أولاً
      _security = AISecurity();
      final securityCheck = await _security.performSystemCheck();
      
      if (!securityCheck.isSecure) {
        debugPrint('❌ فشل فحص الأمان: ${securityCheck.message}');
        return false;
      }
      
      // 2. تهيئة العقل الاصطناعي
      _brain = AIBrain();
      await _brain.initialize();
      
      // 3. تهيئة الذاكرة الذكية
      _memory = AIMemory();
      await _memory.initialize();
      
      // 4. تهيئة معالج الوسائط
      _processor = AIProcessor();
      await _processor.initialize();
      
      // 5. تهيئة مولد الاستجابات
      _responseGenerator = AIResponseGenerator();
      await _responseGenerator.initialize();
      
      // 6. إنشاء جلسة جديدة
      _currentSessionId = _generateSessionId();
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة EduTrack AI بنجاح');
      
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة EduTrack AI: $e');
      return false;
    }
  }

  /// معالجة رسالة المستخدم
  static Future<AIResponse> processMessage(
    String message,
    AppProvider provider, {
    List<dynamic>? attachments,
    Map<String, dynamic>? context,
  }) async {
    if (!_isInitialized) {
      return AIResponse.error('النظام غير مهيأ. يرجى المحاولة لاحقاً.');
    }
    
    if (_isProcessing) {
      return AIResponse.error('النظام مشغول حالياً. يرجى الانتظار قليلاً.');
    }
    
    _isProcessing = true;
    final startTime = DateTime.now();
    
    try {
      _stats['total_interactions'] = (_stats['total_interactions'] as int) + 1;
      
      // 1. فحص الأمان
      final securityResult = await _security.validateInput(message, attachments);
      if (!securityResult.isValid) {
        return AIResponse.error('تم رفض الطلب: ${securityResult.message}');
      }
      
      // 2. فهم الرسالة بالعقل الاصطناعي
      final understanding = await _brain.understand(message, context);
      
      // 3. استرجاع المعلومات من الذاكرة
      final memoryContext = await _memory.getRelevantMemory(
        message,
        _currentSessionId!,
      );
      
      // 4. معالجة المرفقات إن وجدت
      Map<String, dynamic>? processedAttachments;
      if (attachments != null && attachments.isNotEmpty) {
        processedAttachments = await _processor.processAttachments(attachments);
      }
      
      // 5. بناء السياق الشامل
      final fullContext = _buildFullContext(
        provider,
        understanding,
        memoryContext,
        processedAttachments,
        context,
      );
      
      // 6. التفكير واتخاذ القرار
      final decision = await _brain.reason(understanding, fullContext);
      
      // 7. توليد الاستجابة
      final response = await _responseGenerator.generateResponse(
        understanding,
        decision,
        fullContext,
      );
      
      // 8. حفظ التفاعل في الذاكرة
      await _memory.saveInteraction(
        _currentSessionId!,
        message,
        response.message,
        understanding,
        decision,
      );
      
      // 9. تحديث الإحصائيات
      _updateStats(startTime, true);
      
      return response;
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الرسالة: $e');
      _updateStats(startTime, false);
      return AIResponse.error('عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
    } finally {
      _isProcessing = false;
    }
  }

  /// بناء السياق الشامل
  static Map<String, dynamic> _buildFullContext(
    AppProvider provider,
    AIUnderstanding understanding,
    Map<String, dynamic> memoryContext,
    Map<String, dynamic>? processedAttachments,
    Map<String, dynamic>? additionalContext,
  ) {
    return {
      'app_data': {
        'groups': provider.groups.map((g) => {
          'id': g.id,
          'name': g.name,
          'subject': g.subject,
          'student_count': g.studentIds.length,
          'monthly_fee': g.monthlyFee,
        }).toList(),
        'students': provider.students.map((s) => {
          'id': s.id,
          'name': s.name,
          'group_id': s.groupId,
          'is_present': s.isPresent,
          'has_paid': s.hasPaid,
          'monthly_payment': s.monthlyPayment,
        }).toList(),
        'lessons': provider.lessons.map((l) => {
          'id': l.id,
          'group_id': l.groupId,
          'date_time': l.dateTime.toIso8601String(),
          'is_completed': l.isCompleted,
          'attended_students': l.attendedStudentIds.length,
        }).toList(),
      },
      'understanding': understanding.toJson(),
      'memory': memoryContext,
      'attachments': processedAttachments,
      'additional': additionalContext ?? {},
      'session_id': _currentSessionId,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// توليد معرف جلسة فريد
  static String _generateSessionId() {
    final now = DateTime.now();
    return 'edutrack_ai_${now.millisecondsSinceEpoch}_${now.microsecond}';
  }

  /// تحديث الإحصائيات
  static void _updateStats(DateTime startTime, bool success) {
    final duration = DateTime.now().difference(startTime);
    
    if (success) {
      _stats['successful_responses'] = (_stats['successful_responses'] as int) + 1;
    }
    
    final currentAvg = _stats['average_response_time'] as double;
    final totalInteractions = _stats['total_interactions'] as int;
    
    _stats['average_response_time'] = 
        ((currentAvg * (totalInteractions - 1)) + duration.inMilliseconds) / totalInteractions;
    
    _stats['last_interaction'] = DateTime.now().toIso8601String();
  }

  /// الحصول على إحصائيات النظام
  static Map<String, dynamic> getSystemStats() {
    final uptime = DateTime.now().difference(_stats['system_uptime'] as DateTime);
    
    return {
      ..._stats,
      'uptime_hours': uptime.inHours,
      'success_rate': _stats['total_interactions'] > 0 
          ? (_stats['successful_responses'] as int) / (_stats['total_interactions'] as int) * 100
          : 0.0,
      'is_initialized': _isInitialized,
      'is_processing': _isProcessing,
      'current_session': _currentSessionId,
    };
  }

  /// إنهاء الجلسة الحالية
  static Future<void> endCurrentSession() async {
    if (_currentSessionId != null) {
      await _memory.endSession(_currentSessionId!);
      _currentSessionId = _generateSessionId();
    }
  }

  /// إعادة تشغيل النظام
  static Future<bool> restart() async {
    await shutdown();
    return await initialize();
  }

  /// إيقاف النظام
  static Future<void> shutdown() async {
    try {
      if (_currentSessionId != null) {
        await _memory.endSession(_currentSessionId!);
      }
      
      await _memory.dispose();
      await _processor.dispose();
      
      _isInitialized = false;
      _isProcessing = false;
      _currentSessionId = null;
      
      debugPrint('🔄 تم إيقاف EduTrack AI');
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف النظام: $e');
    }
  }

  /// التحقق من حالة النظام
  static bool get isInitialized => _isInitialized;
  static bool get isProcessing => _isProcessing;
  static String? get currentSessionId => _currentSessionId;
}

/// استجابة نظام الذكاء الاصطناعي
class AIResponse {
  final String message;
  final bool isSuccess;
  final AIResponseType type;
  final Map<String, dynamic>? actionData;
  final Map<String, dynamic>? metadata;
  final String? error;

  AIResponse({
    required this.message,
    required this.isSuccess,
    required this.type,
    this.actionData,
    this.metadata,
    this.error,
  });

  factory AIResponse.success(
    String message, {
    AIResponseType type = AIResponseType.text,
    Map<String, dynamic>? actionData,
    Map<String, dynamic>? metadata,
  }) {
    return AIResponse(
      message: message,
      isSuccess: true,
      type: type,
      actionData: actionData,
      metadata: metadata,
    );
  }

  factory AIResponse.error(String error) {
    return AIResponse(
      message: error,
      isSuccess: false,
      type: AIResponseType.error,
      error: error,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'isSuccess': isSuccess,
      'type': type.toString(),
      'actionData': actionData,
      'metadata': metadata,
      'error': error,
    };
  }
}

/// أنواع استجابات الذكاء الاصطناعي
enum AIResponseType {
  text,        // نص عادي
  action,      // إجراء يتطلب تنفيذ
  question,    // سؤال للمستخدم
  suggestion,  // اقتراح
  warning,     // تحذير
  error,       // خطأ
  success,     // نجاح العملية
}
