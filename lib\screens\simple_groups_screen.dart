import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../widgets/simple_background.dart';
import '../widgets/simple_bottom_nav.dart';
import '../theme/simple_theme.dart';
import '../models/group.dart';
import '../models/student.dart';

/// صفحة المجموعات المبسطة للأداء المحسن
class SimpleGroupsScreen extends StatefulWidget {
  const SimpleGroupsScreen({super.key});

  @override
  State<SimpleGroupsScreen> createState() => _SimpleGroupsScreenState();
}

class _SimpleGroupsScreenState extends State<SimpleGroupsScreen> {
  String _searchQuery = '';
  String _selectedSubject = 'الكل';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const SimpleAppBar(
        title: 'المجموعات',
        showBackButton: false,
      ),
      body: SimpleBackground(
        child: Consumer<AppProvider>(
          builder: (context, provider, child) {
            final filteredGroups = _getFilteredGroups(provider.groups);
            
            return Column(
              children: [
                // شريط البحث والفلترة
                _buildSearchAndFilter(),
                
                // قائمة المجموعات
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredGroups.length,
                    itemBuilder: (context, index) {
                      return _buildSimpleGroupCard(
                        filteredGroups[index],
                        provider,
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddGroupDialog(),
        backgroundColor: SimpleTheme.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return SimpleCardBackground(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            onChanged: (value) => setState(() => _searchQuery = value),
            style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
            decoration: InputDecoration(
              hintText: 'البحث في المجموعات...',
              hintStyle: GoogleFonts.cairo(color: SimpleTheme.textMuted),
              prefixIcon: const Icon(Icons.search, color: SimpleTheme.textMuted),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0x30FFFFFF)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0x30FFFFFF)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: SimpleTheme.primary),
              ),
              filled: true,
              fillColor: SimpleTheme.surfaceBg,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // فلتر المواد
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ['الكل', 'رياضيات', 'فيزياء', 'كيمياء', 'أحياء', 'إنجليزي']
                  .map((subject) => _buildFilterChip(subject))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String subject) {
    final isSelected = _selectedSubject == subject;
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(
          subject,
          style: GoogleFonts.cairo(
            color: isSelected ? Colors.white : SimpleTheme.textMuted,
            fontSize: 12,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() => _selectedSubject = subject);
        },
        backgroundColor: SimpleTheme.surfaceBg,
        selectedColor: SimpleTheme.primary,
        checkmarkColor: Colors.white,
        side: BorderSide(
          color: isSelected ? SimpleTheme.primary : Color(0x30FFFFFF),
        ),
      ),
    );
  }

  Widget _buildSimpleGroupCard(Group group, AppProvider provider) {
    final students = provider.getStudentsByGroup(group.id);
    
    return SimpleCardBackground(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToGroupDetails(group),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              // أيقونة المجموعة
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: SimpleTheme.primary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.groups,
                  color: SimpleTheme.primary,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // معلومات المجموعة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      group.name,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: SimpleTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${students.length} طالب • ${group.subject}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: SimpleTheme.textMuted,
                      ),
                    ),
                  ],
                ),
              ),
              
              // سهم التنقل
              const Icon(
                Icons.arrow_forward_ios,
                color: SimpleTheme.textMuted,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Group> _getFilteredGroups(List<Group> groups) {
    return groups.where((group) {
      final matchesSearch = group.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                           group.subject.toLowerCase().contains(_searchQuery.toLowerCase());
      final matchesSubject = _selectedSubject == 'الكل' || group.subject == _selectedSubject;
      return matchesSearch && matchesSubject;
    }).toList();
  }

  void _navigateToGroupDetails(Group group) {
    // التنقل لصفحة تفاصيل المجموعة
    Navigator.pushNamed(context, '/group_details', arguments: group);
  }

  void _showAddGroupDialog() {
    // عرض حوار إضافة مجموعة جديدة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'إضافة مجموعة جديدة',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: Text(
          'سيتم إضافة هذه الميزة قريباً',
          style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'حسناً',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}
