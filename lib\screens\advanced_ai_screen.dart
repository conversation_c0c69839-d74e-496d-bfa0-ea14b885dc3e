import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import '../ai/advanced_ai_core.dart';
import '../providers/app_provider.dart';

/// شاشة الذكاء الاصطناعي المتطورة الجديدة
class AdvancedAIScreen extends StatefulWidget {
  const AdvancedAIScreen({Key? key}) : super(key: key);

  @override
  State<AdvancedAIScreen> createState() => _AdvancedAIScreenState();
}

class _AdvancedAIScreenState extends State<AdvancedAIScreen>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final List<File> _attachments = [];

  bool _isProcessing = false;
  bool _isInitialized = false;
  late AnimationController _typingAnimationController;
  late AnimationController _attachmentAnimationController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeAI();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _attachmentAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  /// تهيئة الذكاء الاصطناعي
  Future<void> _initializeAI() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await SmartAICore.initialize();

      if (success) {
        _addSystemMessage(
          '🚀 مرحباً بك في النظام المتطور للذكاء الاصطناعي!\n\n'
          '✨ الميزات الجديدة:\n'
          '• فهم طبيعي متقدم للغة\n'
          '• ذاكرة ذكية للسياق\n'
          '• دعم الملفات والصور\n'
          '• أمان متقدم\n'
          '• استجابات تكيفية\n\n'
          'كيف يمكنني مساعدتك اليوم؟ 🤖',
          isWelcome: true,
        );
        _isInitialized = true;
      } else {
        _addSystemMessage(
          '⚠️ حدث خطأ في تهيئة النظام المتطور.\n'
          'يرجى المحاولة مرة أخرى لاحقاً.',
          isError: true,
        );
      }
    } catch (e) {
      _addSystemMessage('❌ خطأ في التهيئة: $e', isError: true);
    }

    setState(() {
      _isProcessing = false;
    });
  }

  /// إضافة رسالة النظام
  void _addSystemMessage(
    String message, {
    bool isWelcome = false,
    bool isError = false,
  }) {
    setState(() {
      _messages.add(
        ChatMessage(
          text: message,
          isUser: false,
          timestamp: DateTime.now(),
          isSystem: true,
          isWelcome: isWelcome,
          isError: isError,
        ),
      );
    });
    _scrollToBottom();
  }

  /// إضافة رسالة المستخدم
  void _addUserMessage(String message) {
    setState(() {
      _messages.add(
        ChatMessage(
          text: message,
          isUser: true,
          timestamp: DateTime.now(),
          attachments: List.from(_attachments),
        ),
      );
    });
    _scrollToBottom();
  }

  /// إرسال رسالة
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty && _attachments.isEmpty) return;

    if (!_isInitialized) {
      _showSnackBar('النظام غير مهيأ بعد. يرجى الانتظار...', isError: true);
      return;
    }

    _addUserMessage(message.isEmpty ? '[مرفقات]' : message);
    _messageController.clear();

    setState(() {
      _isProcessing = true;
    });

    try {
      final provider = Provider.of<AppProvider>(context, listen: false);
      final response = await SmartAICore.processMessage(
        message.isEmpty ? 'تحليل المرفقات' : message,
        provider,
        attachments: _attachments.isNotEmpty ? List.from(_attachments) : null,
      );

      setState(() {
        _messages.add(
          ChatMessage(
            text: response.message,
            isUser: false,
            timestamp: DateTime.now(),
            isSuccess: response.isSuccess,
            hasAction: response.hasAction,
            metadata: response.metadata,
          ),
        );
        _isProcessing = false;
      });

      // مسح المرفقات بعد الإرسال
      _clearAttachments();
    } catch (e) {
      setState(() {
        _messages.add(
          ChatMessage(
            text: 'عذراً، حدث خطأ في معالجة طلبك: $e',
            isUser: false,
            timestamp: DateTime.now(),
            isError: true,
          ),
        );
        _isProcessing = false;
      });
    }

    _scrollToBottom();
  }

  /// التمرير إلى الأسفل
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// إضافة مرفق من الكاميرا
  Future<void> _addImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        _addAttachment(File(image.path));
      }
    } catch (e) {
      _showSnackBar('خطأ في التقاط الصورة: $e', isError: true);
    }
  }

  /// إضافة مرفق من المعرض
  Future<void> _addImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        _addAttachment(File(image.path));
      }
    } catch (e) {
      _showSnackBar('خطأ في اختيار الصورة: $e', isError: true);
    }
  }

  /// إضافة ملف
  Future<void> _addFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: true,
      );

      if (result != null) {
        for (final file in result.files) {
          if (file.path != null) {
            _addAttachment(File(file.path!));
          }
        }
      }
    } catch (e) {
      _showSnackBar('خطأ في اختيار الملف: $e', isError: true);
    }
  }

  /// إضافة مرفق
  void _addAttachment(File file) {
    if (_attachments.length >= 5) {
      _showSnackBar('يمكنك إرفاق 5 ملفات كحد أقصى', isError: true);
      return;
    }

    setState(() {
      _attachments.add(file);
    });

    _attachmentAnimationController.forward().then((_) {
      _attachmentAnimationController.reverse();
    });

    _showSnackBar('تم إضافة المرفق: ${file.path.split('/').last}');
  }

  /// إزالة مرفق
  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  /// مسح جميع المرفقات
  void _clearAttachments() {
    setState(() {
      _attachments.clear();
    });
  }

  /// عرض رسالة
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض خيارات المرفقات
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إضافة مرفق',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.camera_alt,
                  label: 'كاميرا',
                  onTap: () {
                    Navigator.pop(context);
                    _addImageFromCamera();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.photo_library,
                  label: 'معرض',
                  onTap: () {
                    Navigator.pop(context);
                    _addImageFromGallery();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.attach_file,
                  label: 'ملف',
                  onTap: () {
                    Navigator.pop(context);
                    _addFile();
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء خيار المرفق
  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(icon, size: 30, color: Theme.of(context).primaryColor),
          ),
          const SizedBox(height: 8),
          Text(label),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعد الذكي المتطور'),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeAI,
            tooltip: 'إعادة تهيئة',
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showSystemInfo,
            tooltip: 'معلومات النظام',
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة الرسائل
          Expanded(
            child: _messages.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length + (_isProcessing ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _messages.length && _isProcessing) {
                        return _buildTypingIndicator();
                      }
                      return _buildMessageBubble(_messages[index]);
                    },
                  ),
          ),

          // منطقة المرفقات
          if (_attachments.isNotEmpty) _buildAttachmentsArea(),

          // منطقة الإدخال
          _buildInputArea(),
        ],
      ),
    );
  }

  /// بناء الحالة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.smart_toy, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'ابدأ محادثة مع المساعد الذكي المتطور',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إرسال نصوص أو صور أو ملفات',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الكتابة
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedBuilder(
                  animation: _typingAnimationController,
                  builder: (context, child) {
                    return Row(
                      children: List.generate(3, (index) {
                        final delay = index * 0.2;
                        final animationValue =
                            (_typingAnimationController.value - delay).clamp(
                              0.0,
                              1.0,
                            );
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          child: Transform.scale(
                            scale: 0.5 + (0.5 * animationValue),
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.grey[600],
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
                const SizedBox(width: 8),
                const Text('يكتب...'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء فقاعة الرسالة
  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: message.isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: message.isError
                  ? Colors.red
                  : message.isWelcome
                  ? Colors.green
                  : Theme.of(context).primaryColor,
              child: Icon(
                message.isError
                    ? Icons.error
                    : message.isWelcome
                    ? Icons.celebration
                    : Icons.smart_toy,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isUser
                    ? Theme.of(context).primaryColor
                    : message.isError
                    ? Colors.red[100]
                    : message.isWelcome
                    ? Colors.green[100]
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: TextStyle(
                      color: message.isUser ? Colors.white : Colors.black87,
                      fontSize: 16,
                    ),
                  ),
                  if (message.attachments.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildMessageAttachments(message.attachments),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      color: message.isUser ? Colors.white70 : Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue,
              child: const Icon(Icons.person, size: 16, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء مرفقات الرسالة
  Widget _buildMessageAttachments(List<File> attachments) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: attachments.map((file) {
        final fileName = file.path.split('/').last;
        final isImage =
            fileName.toLowerCase().endsWith('.jpg') ||
            fileName.toLowerCase().endsWith('.jpeg') ||
            fileName.toLowerCase().endsWith('.png');

        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isImage ? Icons.image : Icons.attach_file,
                size: 16,
                color: Colors.white70,
              ),
              const SizedBox(width: 4),
              Text(
                fileName.length > 20
                    ? '${fileName.substring(0, 20)}...'
                    : fileName,
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// بناء منطقة المرفقات
  Widget _buildAttachmentsArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المرفقات (${_attachments.length})',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: _clearAttachments,
                child: const Text('مسح الكل'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _attachments.length,
              itemBuilder: (context, index) {
                final file = _attachments[index];
                final fileName = file.path.split('/').last;

                return Container(
                  width: 80,
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _getFileIcon(fileName),
                              size: 24,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              fileName.length > 10
                                  ? '${fileName.substring(0, 10)}...'
                                  : fileName,
                              style: const TextStyle(fontSize: 10),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: -5,
                        right: -5,
                        child: GestureDetector(
                          onTap: () => _removeAttachment(index),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء منطقة الإدخال
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: _showAttachmentOptions,
            tooltip: 'إضافة مرفق',
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          FloatingActionButton(
            mini: true,
            onPressed: _isProcessing ? null : _sendMessage,
            child: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.send),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الملف
  IconData _getFileIcon(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      case 'mp4':
      case 'avi':
        return Icons.video_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// عرض معلومات النظام
  void _showSystemInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات النظام'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحالة: ${_isInitialized ? "مهيأ" : "غير مهيأ"}'),
            Text('الجلسة: ${SmartAICore.currentSessionId ?? "غير متاح"}'),
            const Text('الإصدار: 2.0.0'),
            const Text('النموذج: Gemini 1.5 Pro/Flash'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _typingAnimationController.dispose();
    _attachmentAnimationController.dispose();
    super.dispose();
  }
}

/// رسالة الدردشة
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isSuccess;
  final bool hasAction;
  final bool isSystem;
  final bool isWelcome;
  final bool isError;
  final List<File> attachments;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isSuccess = true,
    this.hasAction = false,
    this.isSystem = false,
    this.isWelcome = false,
    this.isError = false,
    this.attachments = const [],
    this.metadata,
  });
}
