import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/security_utils.dart';

/// معالج الوسائط المتعددة لـ EduTrack AI
/// يتعامل مع الصور والملفات والوسائط المختلفة
class AIProcessor {
  /// نموذج الرؤية
  GenerativeModel? _visionModel;
  
  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();
  
  /// أنواع الملفات المدعومة
  static const Map<String, List<String>> _supportedTypes = {
    'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    'documents': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
    'spreadsheets': ['.xls', '.xlsx', '.csv'],
    'presentations': ['.ppt', '.pptx'],
    'audio': ['.mp3', '.wav', '.m4a', '.aac'],
    'video': ['.mp4', '.avi', '.mov', '.mkv'],
  };
  
  /// حدود الحجم (بالبايت)
  static const Map<String, int> _sizeLimits = {
    'images': 10 * 1024 * 1024,      // 10 ميجا
    'documents': 50 * 1024 * 1024,   // 50 ميجا
    'spreadsheets': 20 * 1024 * 1024, // 20 ميجا
    'presentations': 30 * 1024 * 1024, // 30 ميجا
    'audio': 100 * 1024 * 1024,      // 100 ميجا
    'video': 500 * 1024 * 1024,      // 500 ميجا
  };

  /// تهيئة معالج الوسائط
  Future<void> initialize() async {
    try {
      // تهيئة نموذج الرؤية
      _visionModel = GenerativeModel(
        model: 'gemini-1.5-pro-vision-latest',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.4,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
        ),
      );

      debugPrint('👁️ تم تهيئة معالج الوسائط المتعددة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة معالج الوسائط: $e');
      rethrow;
    }
  }

  /// معالجة المرفقات
  Future<Map<String, dynamic>> processAttachments(List<dynamic> attachments) async {
    try {
      final results = <String, dynamic>{
        'processed_count': 0,
        'successful_count': 0,
        'failed_count': 0,
        'results': <Map<String, dynamic>>[],
        'summary': '',
      };

      for (int i = 0; i < attachments.length; i++) {
        final attachment = attachments[i];
        results['processed_count'] = i + 1;

        try {
          final result = await _processAttachment(attachment);
          results['results'].add(result);
          
          if (result['success'] == true) {
            results['successful_count'] = (results['successful_count'] as int) + 1;
          } else {
            results['failed_count'] = (results['failed_count'] as int) + 1;
          }
        } catch (e) {
          results['failed_count'] = (results['failed_count'] as int) + 1;
          results['results'].add({
            'success': false,
            'error': 'خطأ في معالجة المرفق: $e',
            'type': 'unknown',
          });
        }
      }

      // إنشاء ملخص
      results['summary'] = _generateProcessingSummary(results);

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في معالجة المرفقات: $e');
      return {
        'processed_count': 0,
        'successful_count': 0,
        'failed_count': attachments.length,
        'results': [],
        'summary': 'فشل في معالجة المرفقات',
        'error': e.toString(),
      };
    }
  }

  /// معالجة مرفق واحد
  Future<Map<String, dynamic>> _processAttachment(dynamic attachment) async {
    try {
      // تحديد نوع المرفق
      final fileType = _detectFileType(attachment);
      
      // التحقق من الدعم
      if (!_isSupported(fileType)) {
        return {
          'success': false,
          'error': 'نوع الملف غير مدعوم: $fileType',
          'type': fileType,
        };
      }

      // التحقق من الحجم
      final sizeCheck = _checkFileSize(attachment, fileType);
      if (!sizeCheck['valid']) {
        return {
          'success': false,
          'error': sizeCheck['error'],
          'type': fileType,
        };
      }

      // معالجة حسب النوع
      switch (_getFileCategory(fileType)) {
        case 'images':
          return await _processImage(attachment);
        case 'documents':
          return await _processDocument(attachment);
        case 'spreadsheets':
          return await _processSpreadsheet(attachment);
        case 'presentations':
          return await _processPresentation(attachment);
        case 'audio':
          return await _processAudio(attachment);
        case 'video':
          return await _processVideo(attachment);
        default:
          return {
            'success': false,
            'error': 'نوع ملف غير معروف',
            'type': fileType,
          };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في معالجة المرفق: $e',
        'type': 'unknown',
      };
    }
  }

  /// معالجة الصور
  Future<Map<String, dynamic>> _processImage(dynamic imageData) async {
    try {
      // تحويل الصورة إلى bytes
      Uint8List imageBytes;
      if (imageData is File) {
        imageBytes = await imageData.readAsBytes();
      } else if (imageData is Uint8List) {
        imageBytes = imageData;
      } else {
        throw Exception('نوع بيانات الصورة غير مدعوم');
      }

      // تحليل الصورة بالذكاء الاصطناعي
      final analysis = await _analyzeImageWithAI(imageBytes);
      
      // استخراج النص من الصورة (OCR)
      final extractedText = await _extractTextFromImage(imageBytes);

      return {
        'success': true,
        'type': 'image',
        'analysis': analysis,
        'extracted_text': extractedText,
        'size': imageBytes.length,
        'format': _detectImageFormat(imageBytes),
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة الصورة: $e',
        'type': 'image',
      };
    }
  }

  /// تحليل الصورة بالذكاء الاصطناعي
  Future<Map<String, dynamic>> _analyzeImageWithAI(Uint8List imageBytes) async {
    if (_visionModel == null) {
      throw Exception('نموذج الرؤية غير مهيأ');
    }

    try {
      final prompt = '''
حلل هذه الصورة بعناية وحدد:

1. المحتوى الرئيسي (ما تراه في الصورة)
2. هل تحتوي على نص؟ إذا كان كذلك، اقرأه
3. هل هي متعلقة بالتعليم؟ (جداول، رسوم بيانية، ملاحظات، إلخ)
4. الألوان الرئيسية
5. جودة الصورة (واضحة، ضبابية، إلخ)
6. أي معلومات مفيدة أخرى

أجب بتنسيق JSON:
{
  "content_description": "وصف المحتوى",
  "contains_text": true/false,
  "extracted_text": "النص المستخرج",
  "educational_content": true/false,
  "educational_type": "نوع المحتوى التعليمي",
  "main_colors": ["لون1", "لون2"],
  "quality": "جودة الصورة",
  "useful_info": "معلومات إضافية مفيدة"
}
''';

      final content = [
        Content.multi([
          TextPart(prompt),
          DataPart('image/jpeg', imageBytes),
        ])
      ];

      final response = await _visionModel!.generateContent(content);
      return _parseAIResponse(response.text ?? '');
    } catch (e) {
      debugPrint('❌ خطأ في تحليل الصورة: $e');
      return {
        'content_description': 'فشل في التحليل',
        'contains_text': false,
        'extracted_text': '',
        'educational_content': false,
        'error': e.toString(),
      };
    }
  }

  /// استخراج النص من الصورة
  Future<String> _extractTextFromImage(Uint8List imageBytes) async {
    try {
      if (_visionModel == null) return '';

      const prompt = '''
استخرج كل النص الموجود في هذه الصورة.
إذا لم يكن هناك نص، أجب بـ "لا يوجد نص".
إذا كان النص بالعربية، احتفظ بالتشكيل إن وجد.
''';

      final content = [
        Content.multi([
          TextPart(prompt),
          DataPart('image/jpeg', imageBytes),
        ])
      ];

      final response = await _visionModel!.generateContent(content);
      final text = response.text ?? '';
      
      return text.trim() == 'لا يوجد نص' ? '' : text.trim();
    } catch (e) {
      debugPrint('❌ خطأ في استخراج النص: $e');
      return '';
    }
  }

  /// معالجة المستندات
  Future<Map<String, dynamic>> _processDocument(dynamic document) async {
    try {
      // محاكاة معالجة المستندات
      return {
        'success': true,
        'type': 'document',
        'content': 'محتوى المستند المستخرج',
        'pages': 1,
        'word_count': 100,
        'summary': 'ملخص المستند',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة المستند: $e',
        'type': 'document',
      };
    }
  }

  /// معالجة جداول البيانات
  Future<Map<String, dynamic>> _processSpreadsheet(dynamic spreadsheet) async {
    try {
      // محاكاة معالجة جداول البيانات
      return {
        'success': true,
        'type': 'spreadsheet',
        'sheets': ['Sheet1', 'Sheet2'],
        'rows': 50,
        'columns': 10,
        'data_summary': 'ملخص البيانات',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة جدول البيانات: $e',
        'type': 'spreadsheet',
      };
    }
  }

  /// معالجة العروض التقديمية
  Future<Map<String, dynamic>> _processPresentation(dynamic presentation) async {
    try {
      // محاكاة معالجة العروض التقديمية
      return {
        'success': true,
        'type': 'presentation',
        'slides': 10,
        'title': 'عنوان العرض',
        'summary': 'ملخص العرض التقديمي',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة العرض التقديمي: $e',
        'type': 'presentation',
      };
    }
  }

  /// معالجة الملفات الصوتية
  Future<Map<String, dynamic>> _processAudio(dynamic audio) async {
    try {
      // محاكاة معالجة الصوت
      return {
        'success': true,
        'type': 'audio',
        'duration': '00:05:30',
        'transcription': 'النص المحول من الصوت',
        'language': 'العربية',
        'quality': 'جيدة',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة الملف الصوتي: $e',
        'type': 'audio',
      };
    }
  }

  /// معالجة ملفات الفيديو
  Future<Map<String, dynamic>> _processVideo(dynamic video) async {
    try {
      // محاكاة معالجة الفيديو
      return {
        'success': true,
        'type': 'video',
        'duration': '00:10:45',
        'resolution': '1920x1080',
        'frames_analyzed': 10,
        'content_summary': 'ملخص محتوى الفيديو',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'فشل في معالجة الفيديو: $e',
        'type': 'video',
      };
    }
  }

  /// تحديد نوع الملف
  String _detectFileType(dynamic file) {
    // محاكاة تحديد نوع الملف
    if (file is File) {
      return file.path.split('.').last.toLowerCase();
    }
    return 'unknown';
  }

  /// التحقق من الدعم
  bool _isSupported(String fileType) {
    return _supportedTypes.values.any((types) => types.contains('.$fileType'));
  }

  /// الحصول على فئة الملف
  String _getFileCategory(String fileType) {
    for (final entry in _supportedTypes.entries) {
      if (entry.value.contains('.$fileType')) {
        return entry.key;
      }
    }
    return 'unknown';
  }

  /// فحص حجم الملف
  Map<String, dynamic> _checkFileSize(dynamic file, String fileType) {
    try {
      int fileSize = 0;
      
      if (file is File) {
        fileSize = file.lengthSync();
      } else if (file is Uint8List) {
        fileSize = file.length;
      }

      final category = _getFileCategory(fileType);
      final limit = _sizeLimits[category] ?? 10 * 1024 * 1024;

      if (fileSize > limit) {
        return {
          'valid': false,
          'error': 'حجم الملف كبير جداً (${_formatFileSize(fileSize)} > ${_formatFileSize(limit)})',
        };
      }

      return {'valid': true};
    } catch (e) {
      return {
        'valid': false,
        'error': 'خطأ في فحص حجم الملف: $e',
      };
    }
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// تحديد تنسيق الصورة
  String _detectImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return 'unknown';
    
    // فحص التوقيعات
    if (bytes[0] == 0xFF && bytes[1] == 0xD8) return 'JPEG';
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) return 'PNG';
    if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46) return 'GIF';
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) return 'BMP';
    
    return 'unknown';
  }

  /// تحليل استجابة الذكاء الاصطناعي
  Map<String, dynamic> _parseAIResponse(String response) {
    try {
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        return jsonDecode(jsonMatch.group(0)!);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة الذكاء الاصطناعي: $e');
    }
    
    return {
      'content_description': response,
      'contains_text': false,
      'extracted_text': '',
      'educational_content': false,
    };
  }

  /// إنشاء ملخص المعالجة
  String _generateProcessingSummary(Map<String, dynamic> results) {
    final total = results['processed_count'] as int;
    final successful = results['successful_count'] as int;
    final failed = results['failed_count'] as int;

    if (total == 0) return 'لم يتم معالجة أي مرفقات';
    
    if (failed == 0) {
      return 'تم معالجة جميع المرفقات بنجاح ($total/$total)';
    } else if (successful == 0) {
      return 'فشل في معالجة جميع المرفقات ($failed/$total)';
    } else {
      return 'تم معالجة $successful من أصل $total مرفق بنجاح، فشل $failed';
    }
  }

  /// تحرير الموارد
  Future<void> dispose() async {
    try {
      // تنظيف الموارد
      debugPrint('🔄 تم تحرير موارد معالج الوسائط');
    } catch (e) {
      debugPrint('❌ خطأ في تحرير معالج الوسائط: $e');
    }
  }
}
