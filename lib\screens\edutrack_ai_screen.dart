import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../ai/edutrack_ai_core.dart';
import '../providers/app_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_background.dart';

/// شاشة EduTrack AI الجديدة
/// واجهة مستخدم حديثة وذكية للتفاعل مع الذكاء الاصطناعي
class EduTrackAIScreen extends StatefulWidget {
  const EduTrackAIScreen({super.key});

  @override
  State<EduTrackAIScreen> createState() => _EduTrackAIScreenState();
}

class _EduTrackAIScreenState extends State<EduTrackAIScreen>
    with TickerProviderStateMixin {
  /// متحكمات النص والتمرير
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  /// حالة الشاشة
  bool _isLoading = false;
  bool _isInitialized = false;
  List<ChatMessage> _messages = [];

  /// متحكمات الرسوم المتحركة
  late AnimationController _typingController;
  late AnimationController _pulseController;
  late Animation<double> _typingAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeAI();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _typingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _typingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _typingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _typingController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  /// تهيئة الذكاء الاصطناعي
  Future<void> _initializeAI() async {
    setState(() => _isLoading = true);

    try {
      final success = await EduTrackAI.initialize();
      
      setState(() {
        _isInitialized = success;
        _isLoading = false;
      });

      if (success) {
        _addWelcomeMessage();
      } else {
        _addErrorMessage('فشل في تهيئة النظام. يرجى المحاولة لاحقاً.');
      }
    } catch (e) {
      setState(() {
        _isInitialized = false;
        _isLoading = false;
      });
      _addErrorMessage('خطأ في تهيئة النظام: $e');
    }
  }

  /// إضافة رسالة ترحيب
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage(
      text: '''
🤖 مرحباً! أنا EduTrack AI - مساعدك الذكي الجديد

✨ **ما الجديد:**
• فهم طبيعي متطور للغة العربية
• ذاكرة ذكية تتذكر محادثاتنا
• دعم الصور والملفات
• أمان متقدم وحماية شاملة
• استجابات تكيفية ومخصصة

💡 **جرب أن تسأل:**
• "كم عدد الطلاب الحاضرين اليوم؟"
• "أنشئ تقرير حضور للأسبوع الماضي"
• "أضف طالب جديد اسمه أحمد"

كيف يمكنني مساعدتك اليوم؟
''',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.welcome,
    );

    setState(() {
      _messages.add(welcomeMessage);
    });

    _scrollToBottom();
  }

  /// إضافة رسالة خطأ
  void _addErrorMessage(String error) {
    final errorMessage = ChatMessage(
      text: '❌ $error',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.error,
    );

    setState(() {
      _messages.add(errorMessage);
    });

    _scrollToBottom();
  }

  /// إرسال رسالة
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || !_isInitialized) return;

    // إضافة رسالة المستخدم
    final userMessage = ChatMessage(
      text: message,
      isUser: true,
      timestamp: DateTime.now(),
      type: MessageType.user,
    );

    setState(() {
      _messages.add(userMessage);
      _isLoading = true;
    });

    _messageController.clear();
    _scrollToBottom();
    _startTypingAnimation();

    try {
      final provider = Provider.of<AppProvider>(context, listen: false);
      final response = await EduTrackAI.processMessage(message, provider);

      final aiMessage = ChatMessage(
        text: response.message,
        isUser: false,
        timestamp: DateTime.now(),
        type: response.isSuccess ? MessageType.ai : MessageType.error,
        responseType: response.type,
        actionData: response.actionData,
        metadata: response.metadata,
      );

      setState(() {
        _messages.add(aiMessage);
        _isLoading = false;
      });

      _stopTypingAnimation();
      _scrollToBottom();

      // تأثير اهتزاز للاستجابة الناجحة
      if (response.isSuccess) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      _stopTypingAnimation();
      _addErrorMessage('خطأ في معالجة الرسالة: $e');
    }
  }

  /// بدء رسوم الكتابة المتحركة
  void _startTypingAnimation() {
    _typingController.repeat();
  }

  /// إيقاف رسوم الكتابة المتحركة
  void _stopTypingAnimation() {
    _typingController.stop();
    _typingController.reset();
  }

  /// التمرير إلى الأسفل
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: EnhancedBackground(
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(child: _buildChatArea()),
              _buildInputArea(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق
  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          ),
          const SizedBox(width: 8),
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.smart_toy_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'EduTrack AI',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _isInitialized ? 'متصل ونشط' : 'جاري التهيئة...',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _showSystemStats,
            icon: const Icon(Icons.info_outline, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// بناء منطقة المحادثة
  Widget _buildChatArea() {
    if (_isLoading && _messages.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length + (_isLoading ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _messages.length && _isLoading) {
                  return _buildTypingIndicator();
                }
                return _buildMessageBubble(_messages[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء فقاعة الرسالة
  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildAvatarIcon(false),
          if (!message.isUser) const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: message.isUser
                    ? AppTheme.primaryGradient
                    : LinearGradient(
                        colors: [
                          Colors.grey[100]!,
                          Colors.grey[50]!,
                        ],
                      ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: message.isUser ? Colors.white : AppTheme.textPrimary,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatTime(message.timestamp),
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: message.isUser
                          ? Colors.white.withValues(alpha: 0.7)
                          : AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) const SizedBox(width: 8),
          if (message.isUser) _buildAvatarIcon(true),
        ],
      ),
    );
  }

  /// بناء أيقونة الصورة الرمزية
  Widget _buildAvatarIcon(bool isUser) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: isUser ? AppTheme.primaryGradient : AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy_rounded,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  /// بناء مؤشر الكتابة
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          _buildAvatarIcon(false),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: AnimatedBuilder(
              animation: _typingAnimation,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTypingDot(0),
                    const SizedBox(width: 4),
                    _buildTypingDot(1),
                    const SizedBox(width: 4),
                    _buildTypingDot(2),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء نقطة الكتابة
  Widget _buildTypingDot(int index) {
    final delay = index * 0.2;
    final animation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(
        parent: _typingController,
        curve: Interval(delay, 1.0, curve: Curves.easeInOut),
      ),
    );

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Opacity(
          opacity: animation.value,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppTheme.primary,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        );
      },
    );
  }

  /// بناء منطقة الإدخال
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              focusNode: _messageFocusNode,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                hintStyle: GoogleFonts.cairo(color: AppTheme.textSecondary),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppTheme.cardBg,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.cairo(color: AppTheme.textPrimary),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
              enabled: _isInitialized && !_isLoading,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(25),
            ),
            child: IconButton(
              onPressed: _isInitialized && !_isLoading ? _sendMessage : null,
              icon: const Icon(Icons.send, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// عرض إحصائيات النظام
  void _showSystemStats() {
    final stats = EduTrackAI.getSystemStats();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إحصائيات النظام',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow('حالة النظام', stats['is_initialized'] ? 'نشط' : 'غير نشط'),
            _buildStatRow('إجمالي التفاعلات', '${stats['total_interactions']}'),
            _buildStatRow('الاستجابات الناجحة', '${stats['successful_responses']}'),
            _buildStatRow('معدل النجاح', '${stats['success_rate'].toStringAsFixed(1)}%'),
            _buildStatRow('متوسط وقت الاستجابة', '${stats['average_response_time'].toStringAsFixed(0)} مللي ثانية'),
            _buildStatRow('وقت التشغيل', '${stats['uptime_hours']} ساعة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  /// بناء صف الإحصائية
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo(fontSize: 12)),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: AppTheme.primary,
            ),
          ),
        ],
      ),
    );
  }
}

/// رسالة المحادثة
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final MessageType type;
  final AIResponseType? responseType;
  final Map<String, dynamic>? actionData;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    required this.type,
    this.responseType,
    this.actionData,
    this.metadata,
  });
}

/// أنواع الرسائل
enum MessageType {
  user,
  ai,
  welcome,
  error,
}
