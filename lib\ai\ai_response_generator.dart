import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/security_utils.dart';
import 'ai_brain.dart';
import 'edutrack_ai_core.dart';

/// مولد الاستجابات الذكية لـ EduTrack AI
/// ينتج استجابات طبيعية ومفيدة ومخصصة
class AIResponseGenerator {
  /// نماذج توليد الاستجابات
  GenerativeModel? _responseModel;
  GenerativeModel? _creativeModel;
  
  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();
  
  /// قوالب الاستجابات
  static const Map<String, String> _responseTemplates = {
    'greeting': '''
مرحباً! أنا مساعدك الذكي في EduTrack 🤖

يمكنني مساعدتك في:
• إدارة الطلاب والمجموعات
• تتبع الحضور والغياب
• إنشاء التقارير والإحصائيات
• جدولة الدروس
• متابعة المدفوعات

كيف يمكنني مساعدتك اليوم؟
''',
    
    'help': '''
إليك ما يمكنني فعله لك:

📚 **إدارة الطلاب:**
- إضافة طلاب جدد
- تعديل معلومات الطلاب
- البحث عن الطلاب

👥 **إدارة المجموعات:**
- إنشاء مجموعات جديدة
- تنظيم الطلاب في المجموعات
- إدارة المواد الدراسية

📊 **التقارير والإحصائيات:**
- تقارير الحضور
- إحصائيات الأداء
- تحليل البيانات

💰 **المدفوعات:**
- تتبع المدفوعات
- تقارير مالية
- تذكيرات الدفع

اكتب طلبك وسأساعدك فوراً!
''',
    
    'error': '''
عذراً، حدث خطأ في معالجة طلبك 😔

يمكنك:
• إعادة صياغة طلبك بطريقة أخرى
• التأكد من صحة المعلومات المدخلة
• المحاولة مرة أخرى بعد قليل

إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.
''',
    
    'success': '''
تم تنفيذ طلبك بنجاح! ✅

هل تحتاج إلى مساعدة أخرى؟
''',
  };

  /// تهيئة مولد الاستجابات
  Future<void> initialize() async {
    try {
      // نموذج الاستجابات العادية
      _responseModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );

      // نموذج الاستجابات الإبداعية
      _creativeModel = GenerativeModel(
        model: 'gemini-1.5-pro',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.9,
          topK: 50,
          topP: 0.98,
          maxOutputTokens: 4096,
        ),
      );

      debugPrint('💬 تم تهيئة مولد الاستجابات الذكية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مولد الاستجابات: $e');
      rethrow;
    }
  }

  /// توليد الاستجابة
  Future<AIResponse> generateResponse(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
  ) async {
    try {
      // اختيار نوع الاستجابة
      final responseType = _determineResponseType(understanding, decision);
      
      // توليد الاستجابة حسب النوع
      switch (responseType) {
        case ResponseGenerationType.template:
          return _generateTemplateResponse(understanding, decision, context);
        
        case ResponseGenerationType.ai_simple:
          return await _generateAIResponse(understanding, decision, context, false);
        
        case ResponseGenerationType.ai_creative:
          return await _generateAIResponse(understanding, decision, context, true);
        
        case ResponseGenerationType.action:
          return _generateActionResponse(understanding, decision, context);
        
        case ResponseGenerationType.data:
          return _generateDataResponse(understanding, decision, context);
        
        default:
          return _generateFallbackResponse(understanding);
      }
    } catch (e) {
      debugPrint('❌ خطأ في توليد الاستجابة: $e');
      return AIResponse.error('عذراً، حدث خطأ في توليد الاستجابة');
    }
  }

  /// تحديد نوع الاستجابة
  ResponseGenerationType _determineResponseType(
    AIUnderstanding understanding,
    AIDecision decision,
  ) {
    // استجابات القوالب للمحادثات البسيطة
    if (understanding.type == MessageType.conversation) {
      if (understanding.intent.contains('تحية') || understanding.intent.contains('مرحبا')) {
        return ResponseGenerationType.template;
      }
      if (understanding.intent.contains('مساعدة') || understanding.intent.contains('help')) {
        return ResponseGenerationType.template;
      }
    }
    
    // استجابات الإجراءات للأوامر
    if (understanding.type == MessageType.command && decision.actions.isNotEmpty) {
      return ResponseGenerationType.action;
    }
    
    // استجابات البيانات للاستعلامات
    if (understanding.type == MessageType.query || understanding.type == MessageType.question) {
      if (understanding.entities.isNotEmpty) {
        return ResponseGenerationType.data;
      }
    }
    
    // استجابات إبداعية للطلبات المعقدة
    if (understanding.complexity == ComplexityLevel.high || understanding.requiresReasoning) {
      return ResponseGenerationType.ai_creative;
    }
    
    // استجابات ذكية بسيطة للباقي
    return ResponseGenerationType.ai_simple;
  }

  /// توليد استجابة من القوالب
  AIResponse _generateTemplateResponse(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
  ) {
    String template = _responseTemplates['help']!;
    
    if (understanding.intent.contains('تحية') || understanding.intent.contains('مرحبا')) {
      template = _responseTemplates['greeting']!;
    } else if (understanding.intent.contains('مساعدة')) {
      template = _responseTemplates['help']!;
    }
    
    // تخصيص القالب
    final customizedResponse = _customizeTemplate(template, context);
    
    return AIResponse.success(
      customizedResponse,
      type: AIResponseType.text,
      metadata: {
        'source': 'template',
        'template_type': understanding.intent,
      },
    );
  }

  /// توليد استجابة بالذكاء الاصطناعي
  Future<AIResponse> _generateAIResponse(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
    bool creative,
  ) async {
    try {
      final model = creative ? _creativeModel! : _responseModel!;
      final prompt = _buildResponsePrompt(understanding, decision, context, creative);
      
      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      final responseText = response.text ?? 'عذراً، لم أتمكن من توليد استجابة';
      
      return AIResponse.success(
        responseText,
        type: AIResponseType.text,
        metadata: {
          'source': creative ? 'ai_creative' : 'ai_simple',
          'model': creative ? 'gemini-1.5-pro' : 'gemini-1.5-flash',
          'prompt_length': prompt.length,
        },
      );
    } catch (e) {
      debugPrint('❌ خطأ في توليد الاستجابة بالذكاء الاصطناعي: $e');
      return _generateFallbackResponse(understanding);
    }
  }

  /// توليد استجابة إجراء
  AIResponse _generateActionResponse(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
  ) {
    final actions = decision.actions;
    if (actions.isEmpty) {
      return _generateFallbackResponse(understanding);
    }

    final actionDescriptions = actions.map((action) {
      final type = action['type'] ?? 'غير محدد';
      final target = action['target'] ?? 'غير محدد';
      return '• $type: $target';
    }).join('\n');

    final responseText = '''
سأقوم بتنفيذ الإجراءات التالية:

$actionDescriptions

${decision.reasoning.isNotEmpty ? '\n**السبب:** ${decision.reasoning}' : ''}

هل تريد المتابعة؟
''';

    return AIResponse.success(
      responseText,
      type: AIResponseType.action,
      actionData: {
        'actions': actions,
        'requires_confirmation': true,
      },
      metadata: {
        'source': 'action_generator',
        'action_count': actions.length,
      },
    );
  }

  /// توليد استجابة بيانات
  AIResponse _generateDataResponse(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
  ) {
    final appData = context['app_data'] as Map<String, dynamic>?;
    if (appData == null) {
      return _generateFallbackResponse(understanding);
    }

    // تحليل البيانات المطلوبة
    final dataAnalysis = _analyzeRequestedData(understanding, appData);
    
    final responseText = _formatDataResponse(dataAnalysis, understanding);

    return AIResponse.success(
      responseText,
      type: AIResponseType.text,
      metadata: {
        'source': 'data_analyzer',
        'data_points': dataAnalysis.length,
      },
    );
  }

  /// بناء برومبت الاستجابة
  String _buildResponsePrompt(
    AIUnderstanding understanding,
    AIDecision decision,
    Map<String, dynamic> context,
    bool creative,
  ) {
    final basePrompt = '''
أنت مساعد ذكي متخصص في أنظمة إدارة التعليم (EduTrack).

**فهم الطلب:**
${jsonEncode(understanding.toJson())}

**قرار النظام:**
${jsonEncode(decision.toJson())}

**السياق:**
${jsonEncode(context)}

**مهمتك:**
قدم استجابة مفيدة ومناسبة باللغة العربية. كن:
- واضحاً ومفهوماً
- مفيداً وعملياً
- مهذباً ومهنياً
- دقيقاً في المعلومات

${creative ? '''
**إرشادات إضافية للاستجابة الإبداعية:**
- استخدم أمثلة توضيحية
- قدم اقتراحات إضافية
- اشرح الخطوات بالتفصيل
- استخدم الرموز التعبيرية المناسبة
''' : '''
**إرشادات للاستجابة البسيطة:**
- كن مختصراً ومباشراً
- ركز على الإجابة الأساسية
- استخدم نقاط واضحة
'''}

الاستجابة:
''';

    return basePrompt;
  }

  /// تخصيص القالب
  String _customizeTemplate(String template, Map<String, dynamic> context) {
    String customized = template;
    
    // إضافة معلومات السياق
    final appData = context['app_data'] as Map<String, dynamic>?;
    if (appData != null) {
      final groups = appData['groups'] as List?;
      final students = appData['students'] as List?;
      
      if (groups != null && students != null) {
        customized += '\n\n📊 **إحصائيات سريعة:**\n';
        customized += '• عدد المجموعات: ${groups.length}\n';
        customized += '• عدد الطلاب: ${students.length}\n';
      }
    }
    
    return customized;
  }

  /// تحليل البيانات المطلوبة
  Map<String, dynamic> _analyzeRequestedData(
    AIUnderstanding understanding,
    Map<String, dynamic> appData,
  ) {
    final analysis = <String, dynamic>{};
    
    // تحليل حسب الكيانات المطلوبة
    for (final entity in understanding.entities) {
      switch (entity) {
        case 'طالب':
          final students = appData['students'] as List? ?? [];
          analysis['students'] = {
            'total': students.length,
            'present': students.where((s) => s['is_present'] == true).length,
            'paid': students.where((s) => s['has_paid'] == true).length,
          };
          break;
          
        case 'مجموعة':
          final groups = appData['groups'] as List? ?? [];
          analysis['groups'] = {
            'total': groups.length,
            'subjects': groups.map((g) => g['subject']).toSet().toList(),
          };
          break;
          
        case 'درس':
          final lessons = appData['lessons'] as List? ?? [];
          analysis['lessons'] = {
            'total': lessons.length,
            'completed': lessons.where((l) => l['is_completed'] == true).length,
            'today': lessons.where((l) {
              final lessonDate = DateTime.tryParse(l['date_time'] ?? '');
              final today = DateTime.now();
              return lessonDate?.day == today.day &&
                     lessonDate?.month == today.month &&
                     lessonDate?.year == today.year;
            }).length,
          };
          break;
      }
    }
    
    return analysis;
  }

  /// تنسيق استجابة البيانات
  String _formatDataResponse(
    Map<String, dynamic> analysis,
    AIUnderstanding understanding,
  ) {
    final buffer = StringBuffer();
    
    buffer.writeln('📊 **إليك المعلومات المطلوبة:**\n');
    
    if (analysis.containsKey('students')) {
      final studentData = analysis['students'] as Map<String, dynamic>;
      buffer.writeln('👥 **الطلاب:**');
      buffer.writeln('• العدد الإجمالي: ${studentData['total']}');
      buffer.writeln('• الحاضرون: ${studentData['present']}');
      buffer.writeln('• الذين دفعوا: ${studentData['paid']}\n');
    }
    
    if (analysis.containsKey('groups')) {
      final groupData = analysis['groups'] as Map<String, dynamic>;
      buffer.writeln('📚 **المجموعات:**');
      buffer.writeln('• العدد الإجمالي: ${groupData['total']}');
      final subjects = groupData['subjects'] as List;
      if (subjects.isNotEmpty) {
        buffer.writeln('• المواد: ${subjects.join(', ')}\n');
      }
    }
    
    if (analysis.containsKey('lessons')) {
      final lessonData = analysis['lessons'] as Map<String, dynamic>;
      buffer.writeln('📖 **الدروس:**');
      buffer.writeln('• العدد الإجمالي: ${lessonData['total']}');
      buffer.writeln('• المكتملة: ${lessonData['completed']}');
      buffer.writeln('• اليوم: ${lessonData['today']}\n');
    }
    
    if (buffer.length <= 50) {
      return 'عذراً، لم أجد البيانات المطلوبة. يرجى التأكد من صحة الطلب.';
    }
    
    buffer.writeln('هل تحتاج إلى تفاصيل أكثر؟');
    
    return buffer.toString();
  }

  /// توليد استجابة احتياطية
  AIResponse _generateFallbackResponse(AIUnderstanding understanding) {
    String message = _responseTemplates['error']!;
    
    // تخصيص الرسالة حسب النوع
    if (understanding.type == MessageType.conversation) {
      message = 'أعتذر، لم أفهم طلبك بوضوح. هل يمكنك إعادة صياغته؟';
    } else if (understanding.type == MessageType.question) {
      message = 'عذراً، لا أملك معلومات كافية للإجابة على سؤالك. هل يمكنك توضيح أكثر؟';
    } else if (understanding.type == MessageType.command) {
      message = 'لم أتمكن من تنفيذ هذا الأمر. يرجى التحقق من صحة الطلب.';
    }
    
    return AIResponse.success(
      message,
      type: AIResponseType.text,
      metadata: {
        'source': 'fallback',
        'original_intent': understanding.intent,
      },
    );
  }
}

/// أنواع توليد الاستجابات
enum ResponseGenerationType {
  template,     // قوالب جاهزة
  ai_simple,    // ذكاء اصطناعي بسيط
  ai_creative,  // ذكاء اصطناعي إبداعي
  action,       // استجابة إجراء
  data,         // استجابة بيانات
}
