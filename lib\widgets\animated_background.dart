import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/simple_theme.dart';

class AnimatedBackground extends StatefulWidget {
  final Widget child;

  const AnimatedBackground({super.key, required this.child});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;
  late AnimationController _controller3;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    try {
      _controller1 = AnimationController(
        duration: const Duration(seconds: 30),
        vsync: this,
      );

      _controller2 = AnimationController(
        duration: const Duration(seconds: 25),
        vsync: this,
      );

      _controller3 = AnimationController(
        duration: const Duration(seconds: 35),
        vsync: this,
      );

      // Start animations with delay to reduce initial load
      Future.delayed(const Duration(milliseconds: 500), () {
        if (!_isDisposed && mounted) {
          _controller1.repeat();
          _controller2.repeat();
          _controller3.repeat();
        }
      });
    } catch (e) {
      // Handle initialization errors
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    try {
      _controller1.dispose();
      _controller2.dispose();
      _controller3.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Base gradient background
        Container(
          decoration: const BoxDecoration(
            gradient: SimpleTheme.backgroundGradient,
          ),
        ),
        // Animated floating elements (only if not disposed)
        if (!_isDisposed)
          RepaintBoundary(
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _controller1,
                _controller2,
                _controller3,
              ]),
              builder: (context, child) {
                return CustomPaint(
                  painter: FloatingElementsPainter(
                    animation1: _controller1.value,
                    animation2: _controller2.value,
                    animation3: _controller3.value,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          ),
        // Content
        widget.child,
      ],
    );
  }
}

class FloatingElementsPainter extends CustomPainter {
  final double animation1;
  final double animation2;
  final double animation3;

  FloatingElementsPainter({
    required this.animation1,
    required this.animation2,
    required this.animation3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint1 = Paint()
      ..color = SimpleTheme.primaryBlue.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final paint2 = Paint()
      ..color = SimpleTheme.primary.withValues(alpha: 0.08)
      ..style = PaintingStyle.fill;

    final paint3 = Paint()
      ..color = SimpleTheme.accentPink.withValues(alpha: 0.06)
      ..style = PaintingStyle.fill;

    // Floating circle 1
    final center1 = Offset(
      size.width * 0.2 + math.sin(animation1 * 2 * math.pi) * 50,
      size.height * 0.3 + math.cos(animation1 * 2 * math.pi) * 30,
    );
    canvas.drawCircle(center1, 80, paint1);

    // Floating circle 2
    final center2 = Offset(
      size.width * 0.8 + math.cos(animation2 * 2 * math.pi) * 40,
      size.height * 0.7 + math.sin(animation2 * 2 * math.pi) * 60,
    );
    canvas.drawCircle(center2, 120, paint2);

    // Floating circle 3
    final center3 = Offset(
      size.width * 0.6 + math.sin(animation3 * 2 * math.pi) * 30,
      size.height * 0.1 + math.cos(animation3 * 2 * math.pi) * 40,
    );
    canvas.drawCircle(center3, 60, paint3);
  }

  @override
  bool shouldRepaint(covariant FloatingElementsPainter oldDelegate) {
    return oldDelegate.animation1 != animation1 ||
        oldDelegate.animation2 != animation2 ||
        oldDelegate.animation3 != animation3;
  }
}
