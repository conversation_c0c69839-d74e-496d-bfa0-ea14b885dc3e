import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:image/image.dart' as img;
import '../utils/security_utils.dart';

/// معالج متعدد الوسائط للذكاء الاصطناعي
class MultimodalProcessor {
  /// نموذج الرؤية
  GenerativeModel? _visionModel;

  /// مفتاح API
  static final String _apiKey = SecurityUtils.getGeminiApiKey();

  /// أنواع الملفات المدعومة
  static const Map<String, List<String>> supportedFileTypes = {
    'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    'documents': ['.pdf', '.txt', '.doc', '.docx', '.rtf'],
    'spreadsheets': ['.xls', '.xlsx', '.csv'],
    'presentations': ['.ppt', '.pptx'],
    'audio': ['.mp3', '.wav', '.m4a', '.aac'],
    'video': ['.mp4', '.avi', '.mov', '.mkv'],
  };

  /// الحد الأقصى لحجم الملف (بالبايت)
  static const int maxFileSize = 20 * 1024 * 1024; // 20 MB

  /// الحد الأقصى لدقة الصورة
  static const int maxImageResolution = 4096;

  /// تهيئة المعالج
  Future<void> initialize() async {
    try {
      _visionModel = GenerativeModel(
        model: 'gemini-1.5-pro-vision-latest',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.4,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
        ),
      );
      debugPrint('✅ تم تهيئة معالج متعدد الوسائط');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة معالج الوسائط: $e');
    }
  }

  /// معالجة المرفقات
  Future<Map<String, dynamic>> processAttachments(
    List<File> attachments,
  ) async {
    try {
      final results = <String, dynamic>{
        'processed_files': [],
        'analysis_results': {},
        'extracted_text': '',
        'file_summaries': [],
        'errors': [],
      };

      for (final file in attachments) {
        try {
          final fileResult = await _processFile(file);
          results['processed_files'].add(fileResult);

          // دمج النتائج
          if (fileResult['extracted_text'] != null) {
            results['extracted_text'] += '${fileResult['extracted_text']}\n';
          }

          if (fileResult['analysis'] != null) {
            results['analysis_results'][file.path] = fileResult['analysis'];
          }

          if (fileResult['summary'] != null) {
            results['file_summaries'].add(fileResult['summary']);
          }
        } catch (e) {
          results['errors'].add({'file': file.path, 'error': e.toString()});
          debugPrint('❌ خطأ في معالجة الملف ${file.path}: $e');
        }
      }

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في معالجة المرفقات: $e');
      return {'error': e.toString()};
    }
  }

  /// معالجة ملف واحد
  Future<Map<String, dynamic>> _processFile(File file) async {
    final fileExtension = _getFileExtension(file.path);
    final fileType = _getFileType(fileExtension);

    // التحقق من حجم الملف
    final fileSize = await file.length();
    if (fileSize > maxFileSize) {
      throw Exception('الملف كبير جداً: ${fileSize / (1024 * 1024)} MB');
    }

    final result = <String, dynamic>{
      'file_path': file.path,
      'file_name': file.path.split('/').last,
      'file_type': fileType,
      'file_size': fileSize,
      'processed_at': DateTime.now().toIso8601String(),
    };

    switch (fileType) {
      case 'image':
        result.addAll(await _processImage(file));
        break;
      case 'document':
        result.addAll(await _processDocument(file));
        break;
      case 'spreadsheet':
        result.addAll(await _processSpreadsheet(file));
        break;
      case 'presentation':
        result.addAll(await _processPresentation(file));
        break;
      case 'audio':
        result.addAll(await _processAudio(file));
        break;
      case 'video':
        result.addAll(await _processVideo(file));
        break;
      default:
        result.addAll(await _processGenericFile(file));
    }

    return result;
  }

  /// معالجة الصور
  Future<Map<String, dynamic>> _processImage(File file) async {
    try {
      final bytes = await file.readAsBytes();

      // تحليل الصورة
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('فشل في قراءة الصورة');
      }

      // تقليل حجم الصورة إذا كانت كبيرة
      img.Image processedImage = image;
      if (image.width > maxImageResolution ||
          image.height > maxImageResolution) {
        final scale =
            maxImageResolution /
            (image.width > image.height ? image.width : image.height);
        processedImage = img.copyResize(
          image,
          width: (image.width * scale).round(),
          height: (image.height * scale).round(),
        );
      }

      // تحويل إلى تنسيق مناسب للذكاء الاصطناعي
      final processedBytes = img.encodeJpg(processedImage, quality: 85);

      // تحليل الصورة بالذكاء الاصطناعي
      final analysis = await _analyzeImageWithAI(processedBytes);

      return {
        'image_info': {
          'width': image.width,
          'height': image.height,
          'format': _getImageFormat(file.path),
          'channels': image.numChannels,
        },
        'analysis': analysis,
        'extracted_text': analysis['text_content'] ?? '',
        'summary': analysis['description'] ?? 'صورة تم تحليلها',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة الصورة: $e');
    }
  }

  /// تحليل الصورة بالذكاء الاصطناعي
  Future<Map<String, dynamic>> _analyzeImageWithAI(Uint8List imageBytes) async {
    if (_visionModel == null) {
      throw Exception('نموذج الرؤية غير مهيأ');
    }

    try {
      final prompt = '''
حلل هذه الصورة بالتفصيل وأعطني:
1. وصف شامل لمحتوى الصورة
2. أي نص موجود في الصورة
3. الألوان الرئيسية
4. العناصر المهمة
5. السياق أو الموضوع
6. أي معلومات مفيدة أخرى

أجب بتنسيق JSON:
{
  "description": "وصف الصورة",
  "text_content": "أي نص في الصورة",
  "main_colors": ["لون1", "لون2"],
  "key_elements": ["عنصر1", "عنصر2"],
  "context": "السياق",
  "additional_info": "معلومات إضافية"
}
''';

      final content = [
        Content.multi([TextPart(prompt), DataPart('image/jpeg', imageBytes)]),
      ];

      final response = await _visionModel!.generateContent(content);
      final responseText = response.text ?? '';

      return _parseAIResponse(responseText);
    } catch (e) {
      debugPrint('❌ خطأ في تحليل الصورة بالذكاء الاصطناعي: $e');
      return {
        'description': 'فشل في تحليل الصورة',
        'text_content': '',
        'error': e.toString(),
      };
    }
  }

  /// معالجة المستندات
  Future<Map<String, dynamic>> _processDocument(File file) async {
    try {
      final extension = _getFileExtension(file.path);
      String extractedText = '';

      switch (extension) {
        case '.txt':
          extractedText = await file.readAsString();
          break;
        case '.pdf':
          extractedText = await _extractTextFromPDF(file);
          break;
        case '.doc':
        case '.docx':
          extractedText = await _extractTextFromWord(file);
          break;
        default:
          extractedText = 'نوع مستند غير مدعوم: $extension';
      }

      // تحليل النص
      final analysis = await _analyzeText(extractedText);

      return {
        'extracted_text': extractedText,
        'word_count': extractedText.split(' ').length,
        'character_count': extractedText.length,
        'analysis': analysis,
        'summary': analysis['summary'] ?? 'مستند تم تحليله',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة المستند: $e');
    }
  }

  /// معالجة جداول البيانات
  Future<Map<String, dynamic>> _processSpreadsheet(File file) async {
    try {
      // محاكاة استخراج البيانات من جدول البيانات
      final data = await _extractDataFromSpreadsheet(file);

      return {
        'extracted_data': data,
        'row_count': data['rows']?.length ?? 0,
        'column_count': data['columns']?.length ?? 0,
        'summary': 'جدول بيانات يحتوي على ${data['rows']?.length ?? 0} صف',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة جدول البيانات: $e');
    }
  }

  /// معالجة العروض التقديمية
  Future<Map<String, dynamic>> _processPresentation(File file) async {
    try {
      // محاكاة استخراج النص من العرض التقديمي
      final extractedText = await _extractTextFromPresentation(file);

      return {
        'extracted_text': extractedText,
        'slide_count': _estimateSlideCount(extractedText),
        'summary': 'عرض تقديمي تم تحليله',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة العرض التقديمي: $e');
    }
  }

  /// معالجة الملفات الصوتية
  Future<Map<String, dynamic>> _processAudio(File file) async {
    try {
      // محاكاة تحويل الصوت إلى نص
      final transcription = await _transcribeAudio(file);

      return {
        'transcription': transcription,
        'duration': await _getAudioDuration(file),
        'summary': 'ملف صوتي تم تحويله إلى نص',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة الملف الصوتي: $e');
    }
  }

  /// معالجة ملفات الفيديو
  Future<Map<String, dynamic>> _processVideo(File file) async {
    try {
      // محاكاة استخراج الإطارات والصوت
      final frames = await _extractVideoFrames(file);
      final audio = await _extractVideoAudio(file);

      return {
        'frame_count': frames.length,
        'audio_transcription': audio,
        'duration': await _getVideoDuration(file),
        'summary': 'ملف فيديو تم تحليله',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة ملف الفيديو: $e');
    }
  }

  /// معالجة الملفات العامة
  Future<Map<String, dynamic>> _processGenericFile(File file) async {
    try {
      final stats = await file.stat();

      return {
        'file_info': {
          'size': stats.size,
          'modified': stats.modified.toIso8601String(),
          'type': stats.type.toString(),
        },
        'summary': 'ملف عام: ${file.path.split('/').last}',
      };
    } catch (e) {
      throw Exception('خطأ في معالجة الملف: $e');
    }
  }

  /// تحليل النص
  Future<Map<String, dynamic>> _analyzeText(String text) async {
    // تحليل بسيط للنص
    final words = text.split(' ');
    final sentences = text.split(RegExp(r'[.!?]+'));

    return {
      'word_count': words.length,
      'sentence_count': sentences.length,
      'average_words_per_sentence': words.length / sentences.length,
      'summary': text.length > 200 ? '${text.substring(0, 200)}...' : text,
      'language': _detectLanguage(text),
    };
  }

  /// استخراج النص من PDF (محاكاة)
  Future<String> _extractTextFromPDF(File file) async {
    // في التطبيق الحقيقي، استخدم مكتبة مثل pdf_text
    return 'نص مستخرج من ملف PDF: ${file.path.split('/').last}';
  }

  /// استخراج النص من Word (محاكاة)
  Future<String> _extractTextFromWord(File file) async {
    // في التطبيق الحقيقي، استخدم مكتبة مناسبة
    return 'نص مستخرج من ملف Word: ${file.path.split('/').last}';
  }

  /// استخراج البيانات من جدول البيانات (محاكاة)
  Future<Map<String, dynamic>> _extractDataFromSpreadsheet(File file) async {
    return {
      'rows': [
        ['العمود 1', 'العمود 2', 'العمود 3'],
        ['بيانات 1', 'بيانات 2', 'بيانات 3'],
      ],
      'columns': ['العمود 1', 'العمود 2', 'العمود 3'],
    };
  }

  /// استخراج النص من العرض التقديمي (محاكاة)
  Future<String> _extractTextFromPresentation(File file) async {
    return 'نص مستخرج من العرض التقديمي: ${file.path.split('/').last}';
  }

  /// تحويل الصوت إلى نص (محاكاة)
  Future<String> _transcribeAudio(File file) async {
    return 'نص محول من الملف الصوتي: ${file.path.split('/').last}';
  }

  /// الحصول على مدة الملف الصوتي (محاكاة)
  Future<Duration> _getAudioDuration(File file) async {
    return const Duration(minutes: 5); // محاكاة
  }

  /// استخراج إطارات الفيديو (محاكاة)
  Future<List<Uint8List>> _extractVideoFrames(File file) async {
    return []; // محاكاة
  }

  /// استخراج الصوت من الفيديو (محاكاة)
  Future<String> _extractVideoAudio(File file) async {
    return 'صوت مستخرج من الفيديو';
  }

  /// الحصول على مدة الفيديو (محاكاة)
  Future<Duration> _getVideoDuration(File file) async {
    return const Duration(minutes: 10); // محاكاة
  }

  /// تقدير عدد الشرائح
  int _estimateSlideCount(String text) {
    // تقدير بسيط بناءً على طول النص
    return (text.length / 500).ceil();
  }

  /// كشف اللغة
  String _detectLanguage(String text) {
    // كشف بسيط للغة
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(text)) {
      return 'العربية';
    } else if (RegExp(r'[a-zA-Z]').hasMatch(text)) {
      return 'الإنجليزية';
    }
    return 'غير محدد';
  }

  /// الحصول على امتداد الملف
  String _getFileExtension(String filePath) {
    return filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
  }

  /// تحديد نوع الملف
  String _getFileType(String extension) {
    for (final entry in supportedFileTypes.entries) {
      if (entry.value.contains(extension)) {
        return entry.key.substring(
          0,
          entry.key.length - 1,
        ); // إزالة 's' من النهاية
      }
    }
    return 'unknown';
  }

  /// الحصول على تنسيق الصورة
  String _getImageFormat(String filePath) {
    final extension = _getFileExtension(filePath);
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'JPEG';
      case '.png':
        return 'PNG';
      case '.gif':
        return 'GIF';
      case '.bmp':
        return 'BMP';
      case '.webp':
        return 'WebP';
      default:
        return 'Unknown';
    }
  }

  /// تحليل استجابة الذكاء الاصطناعي
  Map<String, dynamic> _parseAIResponse(String response) {
    try {
      // البحث عن JSON في الاستجابة
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        return jsonDecode(jsonMatch.group(0)!);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة الذكاء الاصطناعي: $e');
    }

    // إذا فشل التحليل، أرجع استجابة افتراضية
    return {
      'description': response,
      'text_content': '',
      'error': 'فشل في تحليل الاستجابة',
    };
  }

  /// التحقق من دعم نوع الملف
  bool isFileTypeSupported(String filePath) {
    final extension = _getFileExtension(filePath);
    return supportedFileTypes.values.any((types) => types.contains(extension));
  }

  /// الحصول على أنواع الملفات المدعومة
  List<String> getSupportedExtensions() {
    return supportedFileTypes.values.expand((types) => types).toList();
  }
}
