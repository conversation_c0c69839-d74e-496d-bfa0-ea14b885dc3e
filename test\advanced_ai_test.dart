import 'package:flutter_test/flutter_test.dart';
import 'package:edu_track/ai/advanced_ai_core.dart';
import 'package:edu_track/ai/advanced_query_analyzer.dart';
import 'package:edu_track/ai/smart_memory_system.dart';
import 'package:edu_track/ai/advanced_security_system.dart';
import 'package:edu_track/ai/multimodal_processor.dart';
import 'package:edu_track/ai/intelligent_response_generator.dart';

void main() {
  group('Advanced AI System Tests', () {
    group('Query Analyzer Tests', () {
      late AdvancedQueryAnalyzer analyzer;

      setUp(() {
        analyzer = AdvancedQueryAnalyzer();
      });

      test('should analyze simple question correctly', () async {
        const query = 'كم عدد الطلاب في المجموعة الأولى؟';

        final analysis = await analyzer.analyzeQuery(query);

        expect(analysis.type, QueryType.question);
        expect(analysis.entities, contains('طالب'));
        expect(analysis.entities, contains('مجموعة'));
        expect(analysis.complexity, QueryComplexity.low);
        expect(analysis.confidence, greaterThan(0.5));
      });

      test('should analyze complex command correctly', () async {
        const query =
            'أضف طالب جديد اسمه أحمد إلى المجموعة الثانية وحدث حالة الدفع';

        final analysis = await analyzer.analyzeQuery(query);

        expect(analysis.type, QueryType.command);
        expect(analysis.intent, contains('إضافة'));
        expect(analysis.isMultiStep, true);
        expect(analysis.complexity, QueryComplexity.high);
        expect(analysis.entities, contains('طالب'));
        expect(analysis.entities, contains('مجموعة'));
      });

      test('should detect conversational queries', () async {
        const query = 'مرحباً، كيف حالك؟';

        final analysis = await analyzer.analyzeQuery(query);

        expect(analysis.type, QueryType.conversation);
        expect(analysis.sentiment, 'إيجابي');
        expect(analysis.complexity, QueryComplexity.low);
      });

      test('should extract keywords correctly', () async {
        const query = 'اعرض تقرير الحضور للطلاب في الأسبوع الماضي';

        final analysis = await analyzer.analyzeQuery(query);

        expect(analysis.keywords, contains('تقرير'));
        expect(analysis.keywords, contains('حضور'));
        expect(analysis.keywords, contains('طلاب'));
        expect(analysis.keywords, contains('أسبوع'));
      });
    });

    group('Memory System Tests', () {
      late SmartMemorySystem memorySystem;

      setUp(() async {
        memorySystem = SmartMemorySystem();
        await memorySystem.initialize();
      });

      tearDown(() async {
        await memorySystem.dispose();
      });

      test('should save and retrieve interactions', () async {
        const sessionId = 'test_session_1';
        const userMessage = 'أضف طالب جديد';
        const aiResponse = 'تم إضافة الطالب بنجاح';

        final analysis = AdvancedQueryAnalysis(
          originalQuery: userMessage,
          type: QueryType.command,
          intent: 'إضافة',
          entities: ['طالب'],
          complexity: QueryComplexity.medium,
          requiresAdvancedReasoning: false,
          isMultiStep: false,
          confidence: 0.9,
          keywords: ['أضف', 'طالب'],
          sentiment: 'محايد',
        );

        await memorySystem.saveInteraction(
          sessionId,
          userMessage,
          aiResponse,
          analysis,
          {'test': 'data'},
        );

        final context = await memorySystem.getRelevantContext(
          'أضف طالب آخر',
          sessionId,
        );

        expect(context['current_session'], isNotNull);
        expect(context['current_session']['total_interactions'], 1);
      });

      test('should find similar interactions', () async {
        const sessionId = 'test_session_2';

        // إضافة عدة تفاعلات مشابهة
        final interactions = [
          'أضف طالب جديد اسمه محمد',
          'أضف طالبة جديدة اسمها فاطمة',
          'احذف الطالب أحمد',
        ];

        for (final interaction in interactions) {
          final analysis = AdvancedQueryAnalysis(
            originalQuery: interaction,
            type: QueryType.command,
            intent: interaction.contains('أضف') ? 'إضافة' : 'حذف',
            entities: ['طالب'],
            complexity: QueryComplexity.medium,
            requiresAdvancedReasoning: false,
            isMultiStep: false,
            confidence: 0.8,
            keywords: interaction.split(' '),
            sentiment: 'محايد',
          );

          await memorySystem.saveInteraction(
            sessionId,
            interaction,
            'تم تنفيذ العملية',
            analysis,
            {},
          );
        }

        final context = await memorySystem.getRelevantContext(
          'أضف طالب جديد',
          sessionId,
        );

        expect(context['similar_interactions'], isNotEmpty);
        expect(context['similar_interactions'].length, greaterThan(0));
      });
    });

    group('Security System Tests', () {
      late AdvancedSecuritySystem securitySystem;

      setUp(() {
        securitySystem = AdvancedSecuritySystem();
      });

      test('should pass security check for normal input', () async {
        const input = 'أضف طالب جديد اسمه أحمد';

        final result = await securitySystem.validateInput(input);

        expect(result.isValid, true);
        expect(result.severity, ThreatSeverity.none);
      });

      test('should block banned words', () async {
        const input = 'كيف يمكنني اختراق النظام؟';

        final result = await securitySystem.validateInput(input);

        expect(result.isValid, false);
        expect(result.severity, ThreatSeverity.high);
        expect(result.reason, contains('كلمات محظورة'));
      });

      test('should detect suspicious patterns', () async {
        const input = '<script>alert("test")</script>';

        final result = await securitySystem.validateInput(input);

        expect(result.isValid, false);
        expect(result.severity, ThreatSeverity.high);
        expect(result.reason, contains('أنماط مشبوهة'));
      });

      test('should limit input length', () async {
        final longInput = 'أ' * 15000; // أطول من الحد المسموح

        final result = await securitySystem.validateInput(longInput);

        expect(result.isValid, false);
        expect(result.severity, ThreatSeverity.low);
        expect(result.reason, contains('طويل جداً'));
      });

      test('should perform comprehensive security check', () async {
        final result = await securitySystem.performSecurityCheck();

        expect(result.isSecure, true);
        expect(result.securityLevel, isNotNull);
      });
    });

    group('Multimodal Processor Tests', () {
      late MultimodalProcessor processor;

      setUp(() async {
        processor = MultimodalProcessor();
        await processor.initialize();
      });

      test('should identify supported file types', () {
        expect(processor.isFileTypeSupported('test.jpg'), true);
        expect(processor.isFileTypeSupported('test.pdf'), true);
        expect(processor.isFileTypeSupported('test.txt'), true);
        expect(processor.isFileTypeSupported('test.xyz'), false);
      });

      test('should return supported extensions', () {
        final extensions = processor.getSupportedExtensions();

        expect(extensions, contains('.jpg'));
        expect(extensions, contains('.pdf'));
        expect(extensions, contains('.txt'));
        expect(extensions, contains('.mp3'));
      });
    });

    group('Response Generator Tests', () {
      late IntelligentResponseGenerator generator;

      setUp(() async {
        generator = IntelligentResponseGenerator();
        await generator.initialize();
      });

      test('should have response templates', () {
        final greeting = generator.getResponseTemplate('greeting');
        final error = generator.getResponseTemplate('error');

        expect(greeting, isNotEmpty);
        expect(error, isNotEmpty);
        expect(greeting, contains('مرحباً'));
        expect(error, contains('عذراً'));
      });

      test('should add custom response template', () {
        const customKey = 'custom_test';
        const customTemplate = 'قالب مخصص للاختبار';

        generator.addResponseTemplate(customKey, customTemplate);

        // في التطبيق الحقيقي، يجب أن يحفظ القالب
        expect(true, true); // اختبار رمزي
      });
    });

    group('Integration Tests', () {
      test('should handle complete AI workflow', () async {
        // اختبار تكامل شامل للنظام

        // 1. تهيئة النظام
        final initResult = await AdvancedAICore.initialize();
        expect(initResult, true);

        // 2. التحقق من حالة التهيئة
        expect(AdvancedAICore.isInitialized, true);
        expect(AdvancedAICore.currentSessionId, isNotNull);

        // 3. تنظيف الموارد
        await AdvancedAICore.dispose();
        expect(AdvancedAICore.isInitialized, false);
      });
    });
  });

  group('Performance Tests', () {
    test('query analysis should complete within reasonable time', () async {
      final analyzer = AdvancedQueryAnalyzer();
      await analyzer.initialize();

      final stopwatch = Stopwatch()..start();

      await analyzer.analyzeQuery('أضف طالب جديد اسمه أحمد');

      stopwatch.stop();

      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // أقل من 5 ثوان
    });

    test('memory operations should be efficient', () async {
      final memorySystem = SmartMemorySystem();
      await memorySystem.initialize();

      final stopwatch = Stopwatch()..start();

      // إضافة 100 تفاعل
      for (int i = 0; i < 100; i++) {
        final analysis = AdvancedQueryAnalysis(
          originalQuery: 'test query $i',
          type: QueryType.general,
          intent: 'test',
          entities: [],
          complexity: QueryComplexity.low,
          requiresAdvancedReasoning: false,
          isMultiStep: false,
          confidence: 0.8,
          keywords: ['test'],
          sentiment: 'محايد',
        );

        await memorySystem.saveInteraction(
          'test_session',
          'test query $i',
          'test response $i',
          analysis,
          {},
        );
      }

      stopwatch.stop();

      expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // أقل من 10 ثوان

      await memorySystem.dispose();
    });
  });

  group('Error Handling Tests', () {
    test('should handle invalid input gracefully', () async {
      final analyzer = AdvancedQueryAnalyzer();

      // اختبار مدخل فارغ
      final emptyResult = await analyzer.analyzeQuery('');
      expect(emptyResult.type, QueryType.general);
      expect(emptyResult.confidence, lessThan(1.0));

      // اختبار مدخل بأحرف خاصة
      final specialResult = await analyzer.analyzeQuery('!@#\$%^&*()');
      expect(specialResult.type, QueryType.general);
    });

    test('should handle security system errors', () async {
      final securitySystem = AdvancedSecuritySystem();

      // اختبار مدخل null (محاكاة)
      final result = await securitySystem.validateInput('');
      expect(result.isValid, true); // مدخل فارغ مقبول
    });

    test('should handle memory system errors', () async {
      final memorySystem = SmartMemorySystem();

      // محاولة الحصول على سياق قبل التهيئة
      final context = await memorySystem.getRelevantContext('test', 'session');
      expect(context, isMap);
      expect(context.isEmpty, true);
    });
  });
}
