import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'providers/app_provider.dart';
import 'models/student.dart';
import 'models/group.dart';
import 'models/lesson.dart';
import 'screens/advanced_ai_screen.dart';
import 'ai/advanced_ai_core.dart';

/// نقطة دخول التطبيق مع النظام المتطور للذكاء الاصطناعي
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // تهيئة Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    // تهيئة Hive
    await Hive.initFlutter();
    
    // تسجيل المحولات
    Hive.registerAdapter(StudentAdapter());
    Hive.registerAdapter(GroupAdapter());
    Hive.registerAdapter(LessonAdapter());
    
    // فتح الصناديق
    await Hive.openBox<Student>('students');
    await Hive.openBox<Group>('groups');
    await Hive.openBox<Lesson>('lessons');
    
    // تهيئة النظام المتطور للذكاء الاصطناعي
    debugPrint('🚀 بدء تهيئة النظام المتطور للذكاء الاصطناعي...');
    final aiInitialized = await AdvancedAICore.initialize();
    
    if (aiInitialized) {
      debugPrint('✅ تم تهيئة النظام المتطور بنجاح');
    } else {
      debugPrint('⚠️ فشل في تهيئة النظام المتطور');
    }
    
    runApp(const EduTrackAdvancedApp());
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    runApp(const EduTrackErrorApp());
  }
}

/// التطبيق الرئيسي مع النظام المتطور
class EduTrackAdvancedApp extends StatelessWidget {
  const EduTrackAdvancedApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppProvider(),
      child: Consumer<AppProvider>(
        builder: (context, provider, child) {
          return MaterialApp(
            title: 'EduTrack - النظام المتطور',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              fontFamily: 'Cairo',
              brightness: Brightness.light,
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness: Brightness.light,
              ),
            ),
            darkTheme: ThemeData(
              primarySwatch: Colors.blue,
              fontFamily: 'Cairo',
              brightness: Brightness.dark,
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.blue,
                brightness: Brightness.dark,
              ),
            ),
            themeMode: provider.themeMode,
            home: const AdvancedAIHomePage(),
          );
        },
      ),
    );
  }
}

/// الصفحة الرئيسية للنظام المتطور
class AdvancedAIHomePage extends StatefulWidget {
  const AdvancedAIHomePage({Key? key}) : super(key: key);

  @override
  State<AdvancedAIHomePage> createState() => _AdvancedAIHomePageState();
}

class _AdvancedAIHomePageState extends State<AdvancedAIHomePage> {
  bool _isAIReady = false;

  @override
  void initState() {
    super.initState();
    _checkAIStatus();
  }

  /// فحص حالة النظام المتطور
  void _checkAIStatus() {
    setState(() {
      _isAIReady = AdvancedAICore.isInitialized;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('EduTrack - النظام المتطور'),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isAIReady ? Icons.smart_toy : Icons.warning),
            onPressed: _showSystemStatus,
            tooltip: 'حالة النظام',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Colors.transparent,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار النظام المتطور
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.7),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.psychology,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 30),
              
              // عنوان النظام
              Text(
                'النظام المتطور للذكاء الاصطناعي',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 10),
              
              // وصف النظام
              Text(
                'نظام ذكي متطور مع قدرات متقدمة\nللفهم الطبيعي والذاكرة الذكية',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // مؤشر حالة النظام
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                decoration: BoxDecoration(
                  color: _isAIReady ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isAIReady ? Icons.check_circle : Icons.hourglass_empty,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isAIReady ? 'النظام جاهز' : 'جاري التهيئة...',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // زر الدخول للنظام المتطور
              ElevatedButton.icon(
                onPressed: _isAIReady ? _openAdvancedAI : null,
                icon: const Icon(Icons.rocket_launch),
                label: const Text('ادخل للنظام المتطور'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // معلومات النظام
              Card(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        'الميزات الجديدة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 15),
                      _buildFeatureItem(Icons.psychology, 'فهم طبيعي متقدم للغة'),
                      _buildFeatureItem(Icons.memory, 'ذاكرة ذكية للسياق'),
                      _buildFeatureItem(Icons.security, 'أمان متقدم شامل'),
                      _buildFeatureItem(Icons.attachment, 'دعم الملفات والصور'),
                      _buildFeatureItem(Icons.auto_awesome, 'استجابات تكيفية'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshAIStatus,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث الحالة'),
      ),
    );
  }

  /// بناء عنصر ميزة
  Widget _buildFeatureItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// فتح النظام المتطور
  void _openAdvancedAI() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdvancedAIScreen(),
      ),
    );
  }

  /// تحديث حالة النظام
  void _refreshAIStatus() async {
    setState(() {
      _isAIReady = false;
    });
    
    // إعادة تهيئة النظام
    final success = await AdvancedAICore.initialize();
    
    setState(() {
      _isAIReady = success;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم تحديث النظام بنجاح' : 'فشل في تحديث النظام'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  /// عرض حالة النظام
  void _showSystemStatus() {
    final stats = AdvancedAICore.getPerformanceStats();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة النظام المتطور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusItem('الحالة', _isAIReady ? 'مهيأ' : 'غير مهيأ'),
            _buildStatusItem('الجلسة', AdvancedAICore.currentSessionId ?? 'غير متاح'),
            _buildStatusItem('إجمالي الطلبات', '${stats['total_requests'] ?? 0}'),
            _buildStatusItem('متوسط وقت الاستجابة', '${stats['average_response_time'] ?? 0} ms'),
            const Divider(),
            const Text(
              'النظام المتطور يتضمن:\n'
              '• محلل استعلامات متقدم\n'
              '• نظام ذاكرة ذكي\n'
              '• أمان متقدم\n'
              '• دعم متعدد الوسائط\n'
              '• مولد استجابات ذكي',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر حالة
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('$label:', style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }
}

/// تطبيق الخطأ
class EduTrackErrorApp extends StatelessWidget {
  const EduTrackErrorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'EduTrack - خطأ',
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 20),
              const Text(
                'حدث خطأ في تهيئة التطبيق',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              const Text(
                'يرجى إعادة تشغيل التطبيق',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
