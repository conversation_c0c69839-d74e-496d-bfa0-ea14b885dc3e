import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'advanced_query_analyzer.dart';

/// نظام الذاكرة الذكي للذكاء الاصطناعي
class SmartMemorySystem {
  /// مسار ملف الذاكرة
  late String _memoryFilePath;
  
  /// ذاكرة الجلسات
  final Map<String, SessionMemory> _sessions = {};
  
  /// ذاكرة السياق العامة
  final Map<String, dynamic> _globalContext = {};
  
  /// ذاكرة التعلم
  final Map<String, LearningData> _learningData = {};
  
  /// الحد الأقصى لعدد التفاعلات المحفوظة لكل جلسة
  static const int maxInteractionsPerSession = 100;
  
  /// الحد الأقصى لعدد الجلسات المحفوظة
  static const int maxSessions = 50;

  /// تهيئة نظام الذاكرة
  Future<void> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _memoryFilePath = '${appDir.path}/ai_memory.json';
      
      await _loadMemoryFromFile();
      debugPrint('✅ تم تهيئة نظام الذاكرة الذكي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام الذاكرة: $e');
    }
  }

  /// حفظ تفاعل جديد
  Future<void> saveInteraction(
    String sessionId,
    String userMessage,
    String aiResponse,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) async {
    try {
      // إنشاء جلسة جديدة إذا لم تكن موجودة
      _sessions[sessionId] ??= SessionMemory(
        sessionId: sessionId,
        startTime: DateTime.now(),
        interactions: [],
      );

      final session = _sessions[sessionId]!;
      
      // إضافة التفاعل الجديد
      final interaction = Interaction(
        timestamp: DateTime.now(),
        userMessage: userMessage,
        aiResponse: aiResponse,
        analysis: analysis,
        context: context,
      );
      
      session.interactions.add(interaction);
      session.lastActivity = DateTime.now();
      
      // تحديث بيانات التعلم
      _updateLearningData(analysis, userMessage, aiResponse);
      
      // تنظيف الذاكرة إذا تجاوزت الحد الأقصى
      _cleanupMemory();
      
      // حفظ في الملف
      await _saveMemoryToFile();
      
      debugPrint('💾 تم حفظ التفاعل في الذاكرة');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ التفاعل: $e');
    }
  }

  /// الحصول على السياق ذي الصلة
  Future<Map<String, dynamic>> getRelevantContext(
    String query,
    String sessionId,
  ) async {
    try {
      final context = <String, dynamic>{};
      
      // السياق من الجلسة الحالية
      if (_sessions.containsKey(sessionId)) {
        context['current_session'] = _getSessionContext(_sessions[sessionId]!);
      }
      
      // السياق من التفاعلات السابقة المشابهة
      context['similar_interactions'] = _findSimilarInteractions(query);
      
      // بيانات التعلم ذات الصلة
      context['learning_insights'] = _getRelevantLearningData(query);
      
      // السياق العام
      context['global_context'] = Map.from(_globalContext);
      
      return context;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع السياق: $e');
      return {};
    }
  }

  /// الحصول على سياق الجلسة
  Map<String, dynamic> _getSessionContext(SessionMemory session) {
    final recentInteractions = session.interactions
        .take(10) // آخر 10 تفاعلات
        .map((i) => {
              'user': i.userMessage,
              'ai': i.aiResponse,
              'timestamp': i.timestamp.toIso8601String(),
              'intent': i.analysis.intent,
            })
        .toList();

    return {
      'session_id': session.sessionId,
      'start_time': session.startTime.toIso8601String(),
      'last_activity': session.lastActivity.toIso8601String(),
      'total_interactions': session.interactions.length,
      'recent_interactions': recentInteractions,
      'session_summary': _generateSessionSummary(session),
    };
  }

  /// البحث عن تفاعلات مشابهة
  List<Map<String, dynamic>> _findSimilarInteractions(String query) {
    final similarInteractions = <Map<String, dynamic>>[];
    final queryWords = query.toLowerCase().split(' ');
    
    for (final session in _sessions.values) {
      for (final interaction in session.interactions) {
        final similarity = _calculateSimilarity(queryWords, interaction.userMessage);
        
        if (similarity > 0.3) { // عتبة التشابه
          similarInteractions.add({
            'user_message': interaction.userMessage,
            'ai_response': interaction.aiResponse,
            'similarity': similarity,
            'timestamp': interaction.timestamp.toIso8601String(),
            'intent': interaction.analysis.intent,
          });
        }
      }
    }
    
    // ترتيب حسب التشابه
    similarInteractions.sort((a, b) => b['similarity'].compareTo(a['similarity']));
    
    return similarInteractions.take(5).toList(); // أفضل 5 تفاعلات مشابهة
  }

  /// حساب التشابه بين النصوص
  double _calculateSimilarity(List<String> queryWords, String text) {
    final textWords = text.toLowerCase().split(' ');
    int commonWords = 0;
    
    for (final word in queryWords) {
      if (textWords.contains(word)) {
        commonWords++;
      }
    }
    
    return commonWords / queryWords.length;
  }

  /// الحصول على بيانات التعلم ذات الصلة
  Map<String, dynamic> _getRelevantLearningData(String query) {
    final relevantData = <String, dynamic>{};
    
    // أنماط الاستعلامات الشائعة
    relevantData['common_patterns'] = _learningData.values
        .where((data) => data.frequency > 5)
        .map((data) => {
              'pattern': data.pattern,
              'frequency': data.frequency,
              'success_rate': data.successRate,
            })
        .toList();
    
    // الكلمات المفتاحية الشائعة
    final queryWords = query.toLowerCase().split(' ');
    relevantData['keyword_insights'] = _getKeywordInsights(queryWords);
    
    return relevantData;
  }

  /// الحصول على رؤى الكلمات المفتاحية
  Map<String, dynamic> _getKeywordInsights(List<String> queryWords) {
    final insights = <String, dynamic>{};
    
    for (final word in queryWords) {
      final learningData = _learningData[word];
      if (learningData != null) {
        insights[word] = {
          'frequency': learningData.frequency,
          'success_rate': learningData.successRate,
          'common_responses': learningData.commonResponses,
        };
      }
    }
    
    return insights;
  }

  /// تحديث بيانات التعلم
  void _updateLearningData(
    AdvancedQueryAnalysis analysis,
    String userMessage,
    String aiResponse,
  ) {
    // تحديث بيانات النوايا
    final intent = analysis.intent;
    _learningData[intent] ??= LearningData(
      pattern: intent,
      frequency: 0,
      successRate: 0.0,
      commonResponses: [],
    );
    
    _learningData[intent]!.frequency++;
    
    // تحديث بيانات الكلمات المفتاحية
    for (final keyword in analysis.keywords) {
      _learningData[keyword] ??= LearningData(
        pattern: keyword,
        frequency: 0,
        successRate: 0.0,
        commonResponses: [],
      );
      
      _learningData[keyword]!.frequency++;
      
      // إضافة الاستجابة الشائعة
      if (!_learningData[keyword]!.commonResponses.contains(aiResponse)) {
        _learningData[keyword]!.commonResponses.add(aiResponse);
        
        // الاحتفاظ بأفضل 3 استجابات فقط
        if (_learningData[keyword]!.commonResponses.length > 3) {
          _learningData[keyword]!.commonResponses.removeAt(0);
        }
      }
    }
  }

  /// توليد ملخص الجلسة
  String _generateSessionSummary(SessionMemory session) {
    if (session.interactions.isEmpty) return 'جلسة فارغة';
    
    final intents = session.interactions
        .map((i) => i.analysis.intent)
        .toSet()
        .toList();
    
    final totalInteractions = session.interactions.length;
    final duration = session.lastActivity.difference(session.startTime);
    
    return 'جلسة تحتوي على $totalInteractions تفاعل، '
           'النوايا الرئيسية: ${intents.join(', ')}، '
           'المدة: ${duration.inMinutes} دقيقة';
  }

  /// تنظيف الذاكرة
  void _cleanupMemory() {
    // تنظيف الجلسات القديمة
    if (_sessions.length > maxSessions) {
      final sortedSessions = _sessions.values.toList()
        ..sort((a, b) => a.lastActivity.compareTo(b.lastActivity));
      
      final sessionsToRemove = sortedSessions.take(_sessions.length - maxSessions);
      for (final session in sessionsToRemove) {
        _sessions.remove(session.sessionId);
      }
    }
    
    // تنظيف التفاعلات القديمة في كل جلسة
    for (final session in _sessions.values) {
      if (session.interactions.length > maxInteractionsPerSession) {
        session.interactions = session.interactions
            .skip(session.interactions.length - maxInteractionsPerSession)
            .toList();
      }
    }
  }

  /// إنهاء جلسة
  Future<void> endSession(String sessionId) async {
    if (_sessions.containsKey(sessionId)) {
      _sessions[sessionId]!.endTime = DateTime.now();
      await _saveMemoryToFile();
      debugPrint('📝 تم إنهاء الجلسة: $sessionId');
    }
  }

  /// حفظ الذاكرة في ملف
  Future<void> _saveMemoryToFile() async {
    try {
      final memoryData = {
        'sessions': _sessions.map((key, value) => MapEntry(key, value.toJson())),
        'global_context': _globalContext,
        'learning_data': _learningData.map((key, value) => MapEntry(key, value.toJson())),
        'last_updated': DateTime.now().toIso8601String(),
      };
      
      final file = File(_memoryFilePath);
      await file.writeAsString(jsonEncode(memoryData));
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الذاكرة: $e');
    }
  }

  /// تحميل الذاكرة من ملف
  Future<void> _loadMemoryFromFile() async {
    try {
      final file = File(_memoryFilePath);
      if (!await file.exists()) return;
      
      final content = await file.readAsString();
      final data = jsonDecode(content);
      
      // تحميل الجلسات
      if (data['sessions'] != null) {
        for (final entry in data['sessions'].entries) {
          _sessions[entry.key] = SessionMemory.fromJson(entry.value);
        }
      }
      
      // تحميل السياق العام
      if (data['global_context'] != null) {
        _globalContext.addAll(Map<String, dynamic>.from(data['global_context']));
      }
      
      // تحميل بيانات التعلم
      if (data['learning_data'] != null) {
        for (final entry in data['learning_data'].entries) {
          _learningData[entry.key] = LearningData.fromJson(entry.value);
        }
      }
      
      debugPrint('📚 تم تحميل الذاكرة من الملف');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الذاكرة: $e');
    }
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    await _saveMemoryToFile();
    _sessions.clear();
    _globalContext.clear();
    _learningData.clear();
    debugPrint('🧹 تم تنظيف نظام الذاكرة');
  }

  /// الحصول على إحصائيات الذاكرة
  Map<String, dynamic> getMemoryStats() {
    return {
      'total_sessions': _sessions.length,
      'total_interactions': _sessions.values
          .map((s) => s.interactions.length)
          .fold(0, (a, b) => a + b),
      'learning_patterns': _learningData.length,
      'memory_file_path': _memoryFilePath,
    };
  }
}

/// ذاكرة الجلسة
class SessionMemory {
  final String sessionId;
  final DateTime startTime;
  DateTime lastActivity;
  DateTime? endTime;
  List<Interaction> interactions;

  SessionMemory({
    required this.sessionId,
    required this.startTime,
    required this.interactions,
  }) : lastActivity = startTime;

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'startTime': startTime.toIso8601String(),
      'lastActivity': lastActivity.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'interactions': interactions.map((i) => i.toJson()).toList(),
    };
  }

  factory SessionMemory.fromJson(Map<String, dynamic> json) {
    return SessionMemory(
      sessionId: json['sessionId'],
      startTime: DateTime.parse(json['startTime']),
      interactions: (json['interactions'] as List)
          .map((i) => Interaction.fromJson(i))
          .toList(),
    )
      ..lastActivity = DateTime.parse(json['lastActivity'])
      ..endTime = json['endTime'] != null ? DateTime.parse(json['endTime']) : null;
  }
}

/// تفاعل واحد
class Interaction {
  final DateTime timestamp;
  final String userMessage;
  final String aiResponse;
  final AdvancedQueryAnalysis analysis;
  final Map<String, dynamic> context;

  Interaction({
    required this.timestamp,
    required this.userMessage,
    required this.aiResponse,
    required this.analysis,
    required this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'analysis': analysis.toJson(),
      'context': context,
    };
  }

  factory Interaction.fromJson(Map<String, dynamic> json) {
    return Interaction(
      timestamp: DateTime.parse(json['timestamp']),
      userMessage: json['userMessage'],
      aiResponse: json['aiResponse'],
      analysis: AdvancedQueryAnalysis(
        originalQuery: json['analysis']['originalQuery'],
        type: QueryType.values.firstWhere(
          (e) => e.toString() == json['analysis']['type'],
          orElse: () => QueryType.general,
        ),
        intent: json['analysis']['intent'],
        entities: List<String>.from(json['analysis']['entities']),
        complexity: QueryComplexity.values.firstWhere(
          (e) => e.toString() == json['analysis']['complexity'],
          orElse: () => QueryComplexity.medium,
        ),
        requiresAdvancedReasoning: json['analysis']['requiresAdvancedReasoning'],
        isMultiStep: json['analysis']['isMultiStep'],
        confidence: json['analysis']['confidence'],
        keywords: List<String>.from(json['analysis']['keywords']),
        sentiment: json['analysis']['sentiment'],
        explanation: json['analysis']['explanation'],
      ),
      context: Map<String, dynamic>.from(json['context']),
    );
  }
}

/// بيانات التعلم
class LearningData {
  String pattern;
  int frequency;
  double successRate;
  List<String> commonResponses;

  LearningData({
    required this.pattern,
    required this.frequency,
    required this.successRate,
    required this.commonResponses,
  });

  Map<String, dynamic> toJson() {
    return {
      'pattern': pattern,
      'frequency': frequency,
      'successRate': successRate,
      'commonResponses': commonResponses,
    };
  }

  factory LearningData.fromJson(Map<String, dynamic> json) {
    return LearningData(
      pattern: json['pattern'],
      frequency: json['frequency'],
      successRate: json['successRate'],
      commonResponses: List<String>.from(json['commonResponses']),
    );
  }
}
