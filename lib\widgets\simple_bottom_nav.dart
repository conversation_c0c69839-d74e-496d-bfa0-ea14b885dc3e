import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';

/// شريط تنقل بسيط وجميل
class SimpleBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  
  const SimpleBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      decoration: const BoxDecoration(
        color: SimpleTheme.cardBg,
        border: Border(
          top: BorderSide(
            color: Color(0x20FFFFFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(
            icon: Icons.home_rounded,
            label: 'الرئيسية',
            index: 0,
            isSelected: currentIndex == 0,
          ),
          _buildNavItem(
            icon: Icons.groups_rounded,
            label: 'المجموعات',
            index: 1,
            isSelected: currentIndex == 1,
          ),
          _buildNavItem(
            icon: Icons.table_chart_rounded,
            label: 'الحضور',
            index: 2,
            isSelected: currentIndex == 2,
          ),
          _buildNavItem(
            icon: Icons.schedule_rounded,
            label: 'الجدولة',
            index: 3,
            isSelected: currentIndex == 3,
          ),
          _buildNavItem(
            icon: Icons.settings_rounded,
            label: 'الإعدادات',
            index: 4,
            isSelected: currentIndex == 4,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة بسيطة
            Icon(
              icon,
              size: 24,
              color: isSelected 
                ? SimpleTheme.primary 
                : SimpleTheme.textMuted,
            ),
            const SizedBox(height: 4),
            // نص بسيط
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected 
                  ? SimpleTheme.primary 
                  : SimpleTheme.textMuted,
              ),
            ),
            // مؤشر بسيط
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(top: 2),
                width: 4,
                height: 4,
                decoration: const BoxDecoration(
                  color: SimpleTheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// شريط تطبيق بسيط
class SimpleAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showBackButton;
  
  const SimpleAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: SimpleTheme.cardBg,
      elevation: 0,
      leading: leading ?? (showBackButton && Navigator.canPop(context)
        ? IconButton(
            icon: const Icon(Icons.arrow_back_ios_rounded),
            color: SimpleTheme.textPrimary,
            onPressed: () => Navigator.pop(context),
          )
        : null),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: SimpleTheme.textPrimary,
        ),
      ),
      actions: actions,
      centerTitle: true,
      // حدود بسيطة
      bottom: const PreferredSize(
        preferredSize: Size.fromHeight(1),
        child: Divider(
          height: 1,
          color: Color(0x20FFFFFF),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 1);
}
