import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'advanced_query_analyzer.dart';
import 'advanced_ai_core.dart';

/// مولد الاستجابات الذكي
class IntelligentResponseGenerator {
  /// قوالب الاستجابات
  static const Map<String, String> responseTemplates = {
    'greeting': 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
    'farewell': 'وداعاً! أتمنى أن أكون قد ساعدتك.',
    'error': 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
    'success': 'تم تنفيذ طلبك بنجاح!',
    'clarification': 'هل يمكنك توضيح طلبك أكثر؟',
  };

  /// تهيئة المولد
  Future<void> initialize() async {
    debugPrint('✅ تم تهيئة مولد الاستجابات الذكي');
  }

  /// توليد استجابة ذكية
  Future<AIResponse> generateResponse(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
    GenerativeModel model,
  ) async {
    try {
      // 1. تحديد نوع الاستجابة المطلوبة
      final responseType = _determineResponseType(analysis);

      // 2. بناء البرومبت المناسب
      final prompt = _buildPrompt(userMessage, analysis, context, responseType);

      // 3. توليد الاستجابة
      final rawResponse = await _generateWithModel(model, prompt);

      // 4. معالجة وتحسين الاستجابة
      final processedResponse = await _processResponse(
        rawResponse,
        analysis,
        context,
      );

      // 5. إضافة البيانات الوصفية
      final metadata = _generateMetadata(analysis, context);

      return AIResponse.success(
        processedResponse['message'],
        hasAction: processedResponse['hasAction'] ?? false,
        actionData: processedResponse['actionData'],
        metadata: metadata,
      );
    } catch (e) {
      debugPrint('❌ خطأ في توليد الاستجابة: $e');
      return AIResponse.error('عذراً، حدث خطأ في توليد الاستجابة');
    }
  }

  /// تحديد نوع الاستجابة
  String _determineResponseType(AdvancedQueryAnalysis analysis) {
    switch (analysis.type) {
      case QueryType.conversation:
        if (analysis.intent.contains('تحية') ||
            analysis.intent.contains('مرحبا')) {
          return 'greeting';
        } else if (analysis.intent.contains('وداع')) {
          return 'farewell';
        }
        return 'conversational';

      case QueryType.command:
        return 'action';

      case QueryType.question:
        return 'informative';

      case QueryType.search:
        return 'search_result';

      default:
        return 'general';
    }
  }

  /// بناء البرومبت
  String _buildPrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
    String responseType,
  ) {
    switch (responseType) {
      case 'conversational':
        return _buildConversationalPrompt(userMessage, analysis, context);

      case 'action':
        return _buildActionPrompt(userMessage, analysis, context);

      case 'informative':
        return _buildInformativePrompt(userMessage, analysis, context);

      case 'search_result':
        return _buildSearchPrompt(userMessage, analysis, context);

      default:
        return _buildGeneralPrompt(userMessage, analysis, context);
    }
  }

  /// بناء برومبت المحادثة
  String _buildConversationalPrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return '''
أنت مساعد ذكي ودود لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$userMessage"
المشاعر: ${analysis.sentiment}

تعامل مع المستخدم بطريقة ودودة ومفيدة. اجعل الاستجابة:
- طبيعية ومحادثة
- مفيدة وإيجابية
- مناسبة للسياق التعليمي
- باللغة العربية

${context['memory_context'] != null ? 'السياق السابق: ${jsonEncode(context['memory_context'])}' : ''}

أجب بطريقة طبيعية ومفيدة.
''';
  }

  /// بناء برومبت الإجراءات
  String _buildActionPrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return '''
أنت مساعد ذكي لنظام إدارة التعليم EduTrack مع قدرات متقدمة لتنفيذ الإجراءات.

🎯 مهمتك: تحليل طلب المستخدم وتنفيذ الإجراء المطلوب بأقصى مرونة ممكنة.

رسالة المستخدم: "$userMessage"
نوع الطلب: ${analysis.type}
النية: ${analysis.intent}
الكيانات: ${analysis.entities.join(', ')}

البيانات الحالية:
${jsonEncode(context['app_data'])}

## قدراتك الكاملة:

### 1. عمليات البيانات:
- إضافة/تعديل/حذف الطلاب والمجموعات والدروس
- نقل الطلاب بين المجموعات
- تحديث حالات الحضور والدفع
- إنشاء تقارير مخصصة
- حساب الإحصائيات والمعدلات

### 2. عمليات متعددة في نفس الوقت:
- تنفيذ عدة إجراءات متتالية
- معالجة مجموعات من البيانات
- تطبيق تغييرات شاملة

### 3. التحليل والتقارير:
- إنشاء تقارير تفصيلية
- حساب الإحصائيات المتقدمة
- تحليل الأداء والاتجاهات

## تعليمات التنفيذ:

1. **المرونة الكاملة**: يمكنك تنفيذ أي عملية يطلبها المستخدم
2. **العمليات المتعددة**: يمكنك تنفيذ عدة إجراءات في نفس الوقت
3. **الذكاء التكيفي**: اقترح حلول إبداعية للمشاكل المعقدة

## تنسيق الاستجابة:

للعمليات البسيطة:
{
  "response": "وصف ما تم تنفيذه",
  "action": {
    "type": "نوع العملية",
    "entity": "الكيان المستهدف",
    "data": {البيانات},
    "operation": "تفاصيل العملية"
  }
}

للعمليات المعقدة:
{
  "response": "وصف شامل للعمليات المنفذة",
  "actions": [
    {
      "type": "نوع العملية الأولى",
      "entity": "الكيان",
      "data": {البيانات},
      "operation": "التفاصيل"
    },
    {
      "type": "نوع العملية الثانية",
      "entity": "الكيان",
      "data": {البيانات},
      "operation": "التفاصيل"
    }
  ],
  "summary": "ملخص جميع العمليات"
}

نفذ الطلب بأقصى ذكاء ومرونة ممكنة.
''';
  }

  /// بناء برومبت المعلومات
  String _buildInformativePrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return '''
أنت مساعد ذكي خبير في نظام إدارة التعليم EduTrack.

سؤال المستخدم: "$userMessage"
الكيانات المطلوبة: ${analysis.entities.join(', ')}

البيانات المتاحة:
${jsonEncode(context['app_data'])}

${context['similar_interactions'] != null ? 'تفاعلات مشابهة سابقة: ${jsonEncode(context['similar_interactions'])}' : ''}

مهمتك:
1. تحليل السؤال بعمق
2. البحث في البيانات المتاحة
3. تقديم إجابة شاملة ومفيدة
4. إضافة رؤى وتوصيات إضافية

اجعل الإجابة:
- دقيقة ومفصلة
- مدعومة بالبيانات الفعلية
- تحتوي على أمثلة عملية
- مفيدة للمستخدم

أجب باللغة العربية بطريقة واضحة ومنظمة.
''';
  }

  /// بناء برومبت البحث
  String _buildSearchPrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return '''
أنت محرك بحث ذكي لنظام إدارة التعليم EduTrack.

استعلام البحث: "$userMessage"
الكلمات المفتاحية: ${analysis.keywords.join(', ')}

البيانات للبحث فيها:
${jsonEncode(context['app_data'])}

مهمتك:
1. البحث في جميع البيانات المتاحة
2. العثور على النتائج ذات الصلة
3. ترتيب النتائج حسب الأهمية
4. تقديم ملخص مفيد

اعرض النتائج بتنسيق منظم:
- النتائج الأكثر صلة أولاً
- معلومات تفصيلية لكل نتيجة
- إحصائيات مفيدة
- اقتراحات للبحث المتقدم

أجب باللغة العربية بطريقة منظمة وواضحة.
''';
  }

  /// بناء برومبت عام
  String _buildGeneralPrompt(
    String userMessage,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return '''
أنت مساعد ذكي متطور لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$userMessage"
تحليل الاستعلام: ${jsonEncode(analysis.toJson())}

السياق المتاح:
${jsonEncode(context)}

مهمتك:
1. فهم طلب المستخدم بعمق
2. تحديد أفضل طريقة للمساعدة
3. تقديم استجابة مفيدة وذكية
4. اقتراح خطوات إضافية إذا لزم الأمر

اجعل الاستجابة:
- مفيدة وعملية
- واضحة ومفهومة
- مناسبة للسياق
- باللغة العربية

أجب بطريقة ذكية ومفيدة.
''';
  }

  /// توليد الاستجابة باستخدام النموذج
  Future<String> _generateWithModel(
    GenerativeModel model,
    String prompt,
  ) async {
    try {
      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      return response.text ?? 'لا توجد استجابة';
    } catch (e) {
      debugPrint('❌ خطأ في توليد الاستجابة: $e');
      throw Exception('فشل في توليد الاستجابة: $e');
    }
  }

  /// معالجة الاستجابة
  Future<Map<String, dynamic>> _processResponse(
    String rawResponse,
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) async {
    try {
      // محاولة تحليل JSON إذا كانت الاستجابة تحتوي على إجراءات
      if (analysis.type == QueryType.command) {
        final jsonMatch = RegExp(
          r'\{.*\}',
          dotAll: true,
        ).firstMatch(rawResponse);
        if (jsonMatch != null) {
          try {
            final jsonData = jsonDecode(jsonMatch.group(0)!);
            return {
              'message': jsonData['response'] ?? rawResponse,
              'hasAction': true,
              'actionData': jsonData,
            };
          } catch (e) {
            debugPrint('❌ خطأ في تحليل JSON: $e');
          }
        }
      }

      // معالجة الاستجابة العادية
      String processedMessage = _enhanceResponse(rawResponse, analysis);

      return {'message': processedMessage, 'hasAction': false};
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الاستجابة: $e');
      return {'message': rawResponse, 'hasAction': false};
    }
  }

  /// تحسين الاستجابة
  String _enhanceResponse(String response, AdvancedQueryAnalysis analysis) {
    // إضافة رموز تعبيرية مناسبة
    String enhanced = response;

    switch (analysis.sentiment) {
      case 'إيجابي':
        if (!enhanced.contains('😊') && !enhanced.contains('✅')) {
          enhanced = '😊 $enhanced';
        }
        break;
      case 'سلبي':
        if (!enhanced.contains('😔') && !enhanced.contains('❌')) {
          enhanced = '😔 $enhanced';
        }
        break;
    }

    // إضافة تحية إذا كانت محادثة
    if (analysis.type == QueryType.conversation &&
        analysis.intent.contains('تحية')) {
      enhanced = '$enhanced\n\nكيف يمكنني مساعدتك اليوم؟ 🤖';
    }

    return enhanced;
  }

  /// توليد البيانات الوصفية
  Map<String, dynamic> _generateMetadata(
    AdvancedQueryAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    return {
      'response_type': _determineResponseType(analysis),
      'confidence': analysis.confidence,
      'processing_time': DateTime.now().toIso8601String(),
      'query_complexity': analysis.complexity.toString(),
      'entities_processed': analysis.entities,
      'context_used': context.keys.toList(),
    };
  }

  /// الحصول على قالب استجابة
  String getResponseTemplate(String templateKey) {
    return responseTemplates[templateKey] ?? responseTemplates['error']!;
  }

  /// إضافة قالب استجابة مخصص
  void addResponseTemplate(String key, String template) {
    // في التطبيق الحقيقي، يمكن حفظ القوالب المخصصة
    debugPrint('تم إضافة قالب استجابة: $key');
  }
}
