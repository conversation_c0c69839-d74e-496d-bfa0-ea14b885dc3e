import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';

/// نظام الأمان المتطور لـ EduTrack AI
/// يحمي النظام من التهديدات والاستخدام الخاطئ
class AISecurity {
  /// قائمة الكلمات المحظورة
  static const List<String> _bannedWords = [
    'hack', 'هاك', 'اختراق', 'فيروس', 'ضرر', 'تدمير',
    'password', 'كلمة مرور', 'سر', 'خاص', 'شخصي',
  ];
  
  /// أنماط مشبوهة
  static const List<String> _suspiciousPatterns = [
    r'<script.*?>.*?</script>',
    r'javascript:',
    r'eval\(',
    r'exec\(',
    r'system\(',
    r'\.\./',
    r'file://',
    r'ftp://',
  ];
  
  /// حدود معدل الطلبات
  static const int _maxRequestsPerMinute = 30;
  static const int _maxRequestsPerHour = 200;
  
  /// تتبع الطلبات
  final Map<String, List<DateTime>> _requestHistory = {};
  
  /// قائمة IP المحظورة
  final Set<String> _blockedIPs = {};
  
  /// إحصائيات الأمان
  final Map<String, int> _securityStats = {
    'total_checks': 0,
    'blocked_requests': 0,
    'suspicious_patterns': 0,
    'rate_limit_violations': 0,
  };

  /// فحص شامل للنظام
  Future<SecurityCheckResult> performSystemCheck() async {
    try {
      _securityStats['total_checks'] = (_securityStats['total_checks'] ?? 0) + 1;
      
      // 1. فحص سلامة التطبيق
      final integrityCheck = await _checkAppIntegrity();
      if (!integrityCheck.isValid) {
        return SecurityCheckResult(
          isSecure: false,
          message: 'فشل فحص سلامة التطبيق: ${integrityCheck.message}',
          threatLevel: ThreatLevel.high,
        );
      }
      
      // 2. فحص البيئة
      final environmentCheck = _checkEnvironment();
      if (!environmentCheck.isValid) {
        return SecurityCheckResult(
          isSecure: false,
          message: 'بيئة غير آمنة: ${environmentCheck.message}',
          threatLevel: ThreatLevel.medium,
        );
      }
      
      // 3. فحص الموارد
      final resourceCheck = _checkResources();
      if (!resourceCheck.isValid) {
        return SecurityCheckResult(
          isSecure: false,
          message: 'مشكلة في الموارد: ${resourceCheck.message}',
          threatLevel: ThreatLevel.low,
        );
      }
      
      return SecurityCheckResult(
        isSecure: true,
        message: 'النظام آمن',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأمان: $e');
      return SecurityCheckResult(
        isSecure: false,
        message: 'خطأ في فحص الأمان',
        threatLevel: ThreatLevel.high,
      );
    }
  }

  /// التحقق من صحة المدخلات
  Future<ValidationResult> validateInput(
    String input,
    List<dynamic>? attachments,
  ) async {
    try {
      // 1. فحص الطول
      if (input.length > 10000) {
        _incrementStat('blocked_requests');
        return ValidationResult(
          isValid: false,
          message: 'النص طويل جداً',
          threatLevel: ThreatLevel.medium,
        );
      }
      
      // 2. فحص الكلمات المحظورة
      final bannedWordCheck = _checkBannedWords(input);
      if (!bannedWordCheck.isValid) {
        _incrementStat('blocked_requests');
        return bannedWordCheck;
      }
      
      // 3. فحص الأنماط المشبوهة
      final patternCheck = _checkSuspiciousPatterns(input);
      if (!patternCheck.isValid) {
        _incrementStat('suspicious_patterns');
        return patternCheck;
      }
      
      // 4. فحص معدل الطلبات
      final rateCheck = _checkRateLimit();
      if (!rateCheck.isValid) {
        _incrementStat('rate_limit_violations');
        return rateCheck;
      }
      
      // 5. فحص المرفقات
      if (attachments != null && attachments.isNotEmpty) {
        final attachmentCheck = await _validateAttachments(attachments);
        if (!attachmentCheck.isValid) {
          _incrementStat('blocked_requests');
          return attachmentCheck;
        }
      }
      
      // تسجيل الطلب
      _recordRequest();
      
      return ValidationResult(
        isValid: true,
        message: 'المدخلات صحيحة',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المدخلات: $e');
      return ValidationResult(
        isValid: false,
        message: 'خطأ في التحقق',
        threatLevel: ThreatLevel.high,
      );
    }
  }

  /// فحص سلامة التطبيق
  Future<ValidationResult> _checkAppIntegrity() async {
    try {
      // فحص التوقيع الرقمي (محاكاة)
      final signature = _generateAppSignature();
      final expectedSignature = _getExpectedSignature();
      
      if (signature != expectedSignature) {
        return ValidationResult(
          isValid: false,
          message: 'التوقيع الرقمي غير صحيح',
          threatLevel: ThreatLevel.high,
        );
      }
      
      return ValidationResult(
        isValid: true,
        message: 'سلامة التطبيق مؤكدة',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'خطأ في فحص السلامة',
        threatLevel: ThreatLevel.high,
      );
    }
  }

  /// فحص البيئة
  ValidationResult _checkEnvironment() {
    try {
      // فحص وضع التطوير
      if (kDebugMode) {
        return ValidationResult(
          isValid: true,
          message: 'وضع التطوير',
          threatLevel: ThreatLevel.none,
        );
      }
      
      // فحص الجذر/الجيلبريك (محاكاة)
      if (_isDeviceRooted()) {
        return ValidationResult(
          isValid: false,
          message: 'الجهاز مكسور الحماية',
          threatLevel: ThreatLevel.high,
        );
      }
      
      return ValidationResult(
        isValid: true,
        message: 'البيئة آمنة',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'خطأ في فحص البيئة',
        threatLevel: ThreatLevel.medium,
      );
    }
  }

  /// فحص الموارد
  ValidationResult _checkResources() {
    try {
      // فحص الذاكرة المتاحة (محاكاة)
      final availableMemory = _getAvailableMemory();
      if (availableMemory < 50) { // أقل من 50 ميجا
        return ValidationResult(
          isValid: false,
          message: 'ذاكرة غير كافية',
          threatLevel: ThreatLevel.medium,
        );
      }
      
      return ValidationResult(
        isValid: true,
        message: 'الموارد كافية',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'خطأ في فحص الموارد',
        threatLevel: ThreatLevel.low,
      );
    }
  }

  /// فحص الكلمات المحظورة
  ValidationResult _checkBannedWords(String input) {
    final lowerInput = input.toLowerCase();
    
    for (final word in _bannedWords) {
      if (lowerInput.contains(word.toLowerCase())) {
        return ValidationResult(
          isValid: false,
          message: 'يحتوي على كلمات محظورة',
          threatLevel: ThreatLevel.medium,
        );
      }
    }
    
    return ValidationResult(
      isValid: true,
      message: 'لا توجد كلمات محظورة',
      threatLevel: ThreatLevel.none,
    );
  }

  /// فحص الأنماط المشبوهة
  ValidationResult _checkSuspiciousPatterns(String input) {
    for (final pattern in _suspiciousPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return ValidationResult(
          isValid: false,
          message: 'يحتوي على أنماط مشبوهة',
          threatLevel: ThreatLevel.high,
        );
      }
    }
    
    return ValidationResult(
      isValid: true,
      message: 'لا توجد أنماط مشبوهة',
      threatLevel: ThreatLevel.none,
    );
  }

  /// فحص معدل الطلبات
  ValidationResult _checkRateLimit() {
    final now = DateTime.now();
    final userId = 'default_user'; // يمكن تخصيصه لاحقاً
    
    // تنظيف الطلبات القديمة
    _cleanupOldRequests(userId, now);
    
    final userRequests = _requestHistory[userId] ?? [];
    
    // فحص الحد الأقصى في الدقيقة
    final lastMinute = now.subtract(const Duration(minutes: 1));
    final recentRequests = userRequests.where((time) => time.isAfter(lastMinute)).length;
    
    if (recentRequests >= _maxRequestsPerMinute) {
      return ValidationResult(
        isValid: false,
        message: 'تجاوز الحد الأقصى للطلبات في الدقيقة',
        threatLevel: ThreatLevel.medium,
      );
    }
    
    // فحص الحد الأقصى في الساعة
    final lastHour = now.subtract(const Duration(hours: 1));
    final hourlyRequests = userRequests.where((time) => time.isAfter(lastHour)).length;
    
    if (hourlyRequests >= _maxRequestsPerHour) {
      return ValidationResult(
        isValid: false,
        message: 'تجاوز الحد الأقصى للطلبات في الساعة',
        threatLevel: ThreatLevel.high,
      );
    }
    
    return ValidationResult(
      isValid: true,
      message: 'معدل الطلبات طبيعي',
      threatLevel: ThreatLevel.none,
    );
  }

  /// التحقق من المرفقات
  Future<ValidationResult> _validateAttachments(List<dynamic> attachments) async {
    try {
      for (final attachment in attachments) {
        // فحص نوع الملف
        if (!_isAllowedFileType(attachment)) {
          return ValidationResult(
            isValid: false,
            message: 'نوع ملف غير مسموح',
            threatLevel: ThreatLevel.medium,
          );
        }
        
        // فحص حجم الملف
        if (!_isValidFileSize(attachment)) {
          return ValidationResult(
            isValid: false,
            message: 'حجم الملف كبير جداً',
            threatLevel: ThreatLevel.low,
          );
        }
      }
      
      return ValidationResult(
        isValid: true,
        message: 'المرفقات صحيحة',
        threatLevel: ThreatLevel.none,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'خطأ في فحص المرفقات',
        threatLevel: ThreatLevel.medium,
      );
    }
  }

  /// تسجيل الطلب
  void _recordRequest() {
    final now = DateTime.now();
    const userId = 'default_user';
    
    _requestHistory.putIfAbsent(userId, () => []).add(now);
  }

  /// تنظيف الطلبات القديمة
  void _cleanupOldRequests(String userId, DateTime now) {
    final userRequests = _requestHistory[userId];
    if (userRequests == null) return;
    
    final cutoff = now.subtract(const Duration(hours: 1));
    userRequests.removeWhere((time) => time.isBefore(cutoff));
  }

  /// توليد توقيع التطبيق
  String _generateAppSignature() {
    const appInfo = 'EduTrack_AI_v1.0';
    final bytes = utf8.encode(appInfo);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// الحصول على التوقيع المتوقع
  String _getExpectedSignature() {
    // في التطبيق الحقيقي، هذا سيكون مخزن بشكل آمن
    return _generateAppSignature();
  }

  /// فحص كسر الحماية (محاكاة)
  bool _isDeviceRooted() {
    // في التطبيق الحقيقي، سيتم فحص ملفات النظام
    return false;
  }

  /// الحصول على الذاكرة المتاحة (محاكاة)
  int _getAvailableMemory() {
    // في التطبيق الحقيقي، سيتم فحص الذاكرة الفعلية
    return Random().nextInt(200) + 100; // 100-300 ميجا
  }

  /// فحص نوع الملف المسموح
  bool _isAllowedFileType(dynamic attachment) {
    const allowedTypes = ['.jpg', '.jpeg', '.png', '.pdf', '.txt', '.docx'];
    // محاكاة فحص نوع الملف
    return true;
  }

  /// فحص حجم الملف
  bool _isValidFileSize(dynamic attachment) {
    const maxSize = 10 * 1024 * 1024; // 10 ميجا
    // محاكاة فحص حجم الملف
    return true;
  }

  /// زيادة إحصائية
  void _incrementStat(String key) {
    _securityStats[key] = (_securityStats[key] ?? 0) + 1;
  }

  /// الحصول على إحصائيات الأمان
  Map<String, dynamic> getSecurityStats() {
    final totalChecks = _securityStats['total_checks'] ?? 1;
    
    return {
      ..._securityStats,
      'success_rate': ((totalChecks - (_securityStats['blocked_requests'] ?? 0)) / totalChecks * 100).toStringAsFixed(2),
      'blocked_ips': _blockedIPs.length,
      'active_sessions': _requestHistory.length,
    };
  }

  /// إعادة تعيين الإحصائيات
  void resetStats() {
    _securityStats.clear();
    _requestHistory.clear();
    _blockedIPs.clear();
  }
}

/// نتيجة فحص الأمان
class SecurityCheckResult {
  final bool isSecure;
  final String message;
  final ThreatLevel threatLevel;

  SecurityCheckResult({
    required this.isSecure,
    required this.message,
    required this.threatLevel,
  });
}

/// نتيجة التحقق من الصحة
class ValidationResult {
  final bool isValid;
  final String message;
  final ThreatLevel threatLevel;

  ValidationResult({
    required this.isValid,
    required this.message,
    required this.threatLevel,
  });
}

/// مستويات التهديد
enum ThreatLevel {
  none,   // لا يوجد تهديد
  low,    // تهديد منخفض
  medium, // تهديد متوسط
  high,   // تهديد عالي
}
