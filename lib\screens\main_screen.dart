import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/connection_indicator.dart';

import '../widgets/enhanced_background.dart';
import '../theme/simple_theme.dart';
import '../widgets/smooth_animations.dart';
import 'home_screen.dart';
import 'schedule_screen.dart';
import 'groups_screen.dart';
import 'settings_screen.dart';
import 'attendance_table_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;

  final List<Widget> _screens = [
    const HomeScreen(),
    const ScheduleScreen(),
    const GroupsScreen(),
    const AttendanceTableScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: EnhancedBackground(
          type: BackgroundType.particles,
          enableAnimation: true,
          intensity: 0.08,
          child: SafeArea(
            child: Column(
              children: [
                // Modern top bar with animation
                SmoothAnimations.smoothEntry(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: SimpleTheme.cardGradient,
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                          spreadRadius: -4,
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        const ConnectionIndicator(),
                        const Spacer(),
                        // زر المساعد الذكي
                        IconButton(
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'الذكاء الاصطناعي غير متوفر حالياً',
                                ),
                              ),
                            );
                          },
                          icon: const Icon(
                            Icons.smart_toy_rounded,
                            color: Colors.white,
                          ),
                          tooltip: 'المساعد الذكي',
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            gradient: SimpleTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: SimpleTheme.primaryBlue.withValues(
                                  alpha: 0.4,
                                ),
                                blurRadius: 16,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: Image.asset(
                                  'lib/logo/icon.png',
                                  width: 20,
                                  height: 20,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.school_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'EduTrack',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Main content
                Expanded(
                  child: Stack(
                    children: [
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) {
                          return FadeTransition(
                            opacity: animation,
                            child: SlideTransition(
                              position:
                                  Tween<Offset>(
                                    begin: const Offset(0.1, 0),
                                    end: Offset.zero,
                                  ).animate(
                                    CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.easeInOut,
                                    ),
                                  ),
                              child: child,
                            ),
                          );
                        },
                        child: _screens[_currentIndex],
                      ),
                      // زر عائم للمساعد الذكي
                      Positioned(
                        bottom: 16,
                        left: 16,
                        child: FloatingActionButton(
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'الذكاء الاصطناعي غير متوفر حالياً',
                                ),
                              ),
                            );
                          },
                          backgroundColor: SimpleTheme.primary,
                          child: const Icon(
                            Icons.smart_toy_rounded,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: _buildModernBottomNavBar(),
      ),
    );
  }

  // بناء شريط التنقل البسيط
  Widget _buildModernBottomNavBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 20),
      height: 70,
      decoration: BoxDecoration(
        color: SimpleTheme.cardBg,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
        // ظل بسيط وخفيف
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSimpleNavItem(0, Icons.home_rounded, 'الرئيسية'),
              _buildSimpleNavItem(1, Icons.calendar_today_rounded, 'الجدول'),
              _buildSimpleNavItem(2, Icons.groups_rounded, 'المجموعات'),
              _buildSimpleNavItem(3, Icons.table_chart_rounded, 'الحضور'),
              _buildSimpleNavItem(4, Icons.settings_rounded, 'الإعدادات'),
            ],
          ),
        ),
      ),
    );
  }

  // بناء عنصر التنقل البسيط
  Widget _buildSimpleNavItem(int index, IconData icon, String label) {
    final isSelected = _currentIndex == index;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            HapticFeedback.lightImpact();
            setState(() => _currentIndex = index);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة بسيطة
                Icon(
                  icon,
                  size: 24,
                  color: isSelected
                      ? SimpleTheme.primary
                      : SimpleTheme.textMuted,
                ),
                const SizedBox(height: 4),
                // نص بسيط
                Text(
                  label,
                  style: GoogleFonts.cairo(
                    fontSize: 11,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected
                        ? SimpleTheme.primary
                        : SimpleTheme.textMuted,
                  ),
                ),
                // مؤشر بسيط
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 4,
                    height: 4,
                    decoration: const BoxDecoration(
                      color: SimpleTheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
