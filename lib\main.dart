import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_core/firebase_core.dart';
import 'services/data_service.dart';
import 'services/storage_service.dart';
import 'providers/app_provider.dart';
import 'screens/startup_screen.dart';
import 'theme/simple_theme.dart';

import 'utils/memory_utils.dart';
import 'utils/performance_utils.dart';
import 'utils/image_optimizer.dart';
import 'utils/render_optimizer.dart';
import 'utils/stability_utils.dart';
import 'widgets/performance_utils.dart' as widget_perf;

// مفتاح التنقل العام
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  try {
    // تهيئة Flutter أولاً
    WidgetsFlutterBinding.ensureInitialized();

    // تهيئة Firebase
    await Firebase.initializeApp();

    // تهيئة أدوات الاستقرار
    StabilityUtils.init();

    // تهيئة أدوات الأداء الجديدة
    await widget_perf.PerformanceUtils.initialize();

    // تشغيل التطبيق
    runApp(const EduTrackApp());

    // تنفيذ العمليات الثقيلة بعد عرض التطبيق
    _initializeAppAsync();
  } catch (e) {
    // تشغيل التطبيق حتى لو فشلت التهيئة
    runApp(const EduTrackApp());
  }
}

Future<void> _initializeAppAsync() async {
  try {
    // تحسينات الأداء الأساسية أولاً
    PerformanceUtils.init();
    RenderOptimizer.applyGlobalOptimizations();

    // تعيين نمط واجهة النظام
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // تعيين اتجاهات الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تهيئة إدارة الذاكرة
    MemoryUtils.init();

    // تحسين كاش الصور باستخدام أدوات الأداء الجديدة
    final isLowEnd = widget_perf.PerformanceUtils.isLowEndDevice;
    final imageCacheSize = isLowEnd ? 15 : 40;
    final imageCacheSizeBytes = widget_perf.PerformanceUtils.imageCacheSize;

    PaintingBinding.instance.imageCache.maximumSize = imageCacheSize;
    PaintingBinding.instance.imageCache.maximumSizeBytes = imageCacheSizeBytes;

    // تهيئة محسن الصور
    ImageOptimizer();

    // تنفيذ العمليات الثقيلة
    Future.microtask(() async {
      try {
        await Future.wait([
          initializeDateFormatting(
            'ar',
            null,
          ).timeout(const Duration(seconds: 5)),
          DataService.init().timeout(const Duration(seconds: 10)),
          StorageService.init().timeout(const Duration(seconds: 10)),
        ]);
      } catch (e) {
        // تجاهل الأخطاء
      }
    });
  } catch (e) {
    // تجاهل أخطاء التهيئة
  }
}

class EduTrackApp extends StatefulWidget {
  const EduTrackApp({super.key});

  @override
  State<EduTrackApp> createState() => _EduTrackAppState();
}

class _EduTrackAppState extends State<EduTrackApp> {
  @override
  void dispose() {
    MemoryUtils.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppProvider(),
      child: Consumer<AppProvider>(
        builder: (context, provider, child) {
          return MaterialApp(
            title: 'EduTrack',
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey,
            theme: SimpleTheme.lightTheme,
            darkTheme: SimpleTheme.darkTheme,
            themeMode: provider.themeMode,
            home: const StartupScreen(),
            // Performance optimizations
            builder: (context, child) {
              // Apply optimized text scaling based on device performance
              final textScale = widget_perf.PerformanceUtils.isLowEndDevice
                  ? 0.95
                  : 1.0;
              return MediaQuery(
                data: MediaQuery.of(
                  context,
                ).copyWith(textScaler: TextScaler.linear(textScale)),
                child: child!,
              );
            },
            // Reduce unnecessary rebuilds
            restorationScopeId: 'edutrack_app',
          );
        },
      ),
    );
  }
}

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.darkBg,
      body: Center(
        child: CircularProgressIndicator(color: SimpleTheme.primary),
      ),
    );
  }
}
