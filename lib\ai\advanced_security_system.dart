import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import '../utils/security_utils.dart';

/// نظام الأمان المتقدم للذكاء الاصطناعي
class AdvancedSecuritySystem {
  /// قائمة الكلمات المحظورة
  static final List<String> _bannedWords = [
    'hack',
    'crack',
    'exploit',
    'malware',
    'virus',
    'اختراق',
    'تخريب',
    'فيروس',
    'ضرر',
    'تدمير',
  ];

  /// قائمة الأنماط المشبوهة
  static final List<RegExp> _suspiciousPatterns = [
    RegExp(r'<script.*?>', caseSensitive: false),
    RegExp(r'javascript:', caseSensitive: false),
    RegExp(r'eval\s*\(', caseSensitive: false),
    RegExp(r'exec\s*\(', caseSensitive: false),
    RegExp(r'system\s*\(', caseSensitive: false),
  ];

  /// حد معدل الطلبات (طلبات في الدقيقة)
  static const int rateLimitPerMinute = 30;

  /// تتبع الطلبات
  final Map<String, List<DateTime>> _requestHistory = {};

  /// قائمة IP المحظورة
  final Set<String> _blockedIPs = {};

  /// مستوى الأمان
  SecurityLevel _securityLevel = SecurityLevel.medium;

  /// إحصائيات الأمان
  final Map<String, int> _securityStats = {
    'total_requests': 0,
    'blocked_requests': 0,
    'suspicious_attempts': 0,
    'rate_limit_violations': 0,
  };

  /// فحص الأمان الشامل
  Future<SecurityCheckResult> performSecurityCheck() async {
    try {
      debugPrint('🔒 بدء فحص الأمان الشامل...');

      // 1. فحص سلامة التطبيق
      final appIntegrityCheck = await _checkAppIntegrity();
      if (!appIntegrityCheck.isValid) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'فشل فحص سلامة التطبيق: ${appIntegrityCheck.reason}',
          securityLevel: SecurityLevel.critical,
        );
      }

      // 2. فحص البيئة
      final environmentCheck = _checkEnvironment();
      if (!environmentCheck.isValid) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'بيئة غير آمنة: ${environmentCheck.reason}',
          securityLevel: SecurityLevel.high,
        );
      }

      // 3. فحص الشبكة
      final networkCheck = await _checkNetworkSecurity();
      if (!networkCheck.isValid) {
        debugPrint('⚠️ تحذير أمني في الشبكة: ${networkCheck.reason}');
        _securityLevel = SecurityLevel.high;
      }

      // 4. فحص الذاكرة
      final memoryCheck = _checkMemorySecurity();
      if (!memoryCheck.isValid) {
        debugPrint('⚠️ تحذير أمني في الذاكرة: ${memoryCheck.reason}');
      }

      debugPrint('✅ تم اجتياز فحص الأمان بنجاح');
      return SecurityCheckResult(
        isValid: true,
        reason: 'تم اجتياز جميع فحوصات الأمان',
        securityLevel: _securityLevel,
      );
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأمان: $e');
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص الأمان: $e',
        securityLevel: SecurityLevel.critical,
      );
    }
  }

  /// التحقق من صحة المدخلات
  Future<ValidationResult> validateInput(
    String input, [
    List<File>? attachments,
  ]) async {
    try {
      _securityStats['total_requests'] = _securityStats['total_requests']! + 1;

      // 1. فحص معدل الطلبات
      final rateLimitCheck = _checkRateLimit();
      if (!rateLimitCheck.isValid) {
        _securityStats['rate_limit_violations'] =
            _securityStats['rate_limit_violations']! + 1;
        return ValidationResult(
          isValid: false,
          reason: 'تم تجاوز حد معدل الطلبات',
          severity: ThreatSeverity.medium,
        );
      }

      // 2. فحص الكلمات المحظورة
      final bannedWordsCheck = _checkBannedWords(input);
      if (!bannedWordsCheck.isValid) {
        _securityStats['blocked_requests'] =
            _securityStats['blocked_requests']! + 1;
        return ValidationResult(
          isValid: false,
          reason: 'تحتوي على كلمات محظورة',
          severity: ThreatSeverity.high,
        );
      }

      // 3. فحص الأنماط المشبوهة
      final suspiciousPatternCheck = _checkSuspiciousPatterns(input);
      if (!suspiciousPatternCheck.isValid) {
        _securityStats['suspicious_attempts'] =
            _securityStats['suspicious_attempts']! + 1;
        return ValidationResult(
          isValid: false,
          reason: 'تحتوي على أنماط مشبوهة',
          severity: ThreatSeverity.high,
        );
      }

      // 4. فحص طول المدخل
      final lengthCheck = _checkInputLength(input);
      if (!lengthCheck.isValid) {
        return ValidationResult(
          isValid: false,
          reason: 'المدخل طويل جداً',
          severity: ThreatSeverity.low,
        );
      }

      // 5. فحص المرفقات
      if (attachments != null) {
        final attachmentCheck = await _checkAttachments(attachments);
        if (!attachmentCheck.isValid) {
          return ValidationResult(
            isValid: false,
            reason: 'مرفقات غير آمنة: ${attachmentCheck.reason}',
            severity: ThreatSeverity.medium,
          );
        }
      }

      // 6. فحص التشفير
      final encryptionCheck = _checkEncryption(input);
      if (!encryptionCheck.isValid) {
        debugPrint('⚠️ تحذير: ${encryptionCheck.reason}');
      }

      return ValidationResult(
        isValid: true,
        reason: 'المدخل آمن',
        severity: ThreatSeverity.none,
      );
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المدخلات: $e');
      return ValidationResult(
        isValid: false,
        reason: 'خطأ في التحقق من الأمان',
        severity: ThreatSeverity.critical,
      );
    }
  }

  /// فحص سلامة التطبيق
  Future<SecurityCheckResult> _checkAppIntegrity() async {
    try {
      // فحص التوقيع الرقمي (محاكاة)
      final signature = await _calculateAppSignature();
      final expectedSignature = SecurityUtils.getExpectedSignature();

      if (signature != expectedSignature) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'التوقيع الرقمي غير صحيح',
        );
      }

      // فحص الملفات الحساسة
      final criticalFiles = ['lib/main.dart', 'pubspec.yaml'];
      for (final filePath in criticalFiles) {
        final file = File(filePath);
        if (!await file.exists()) {
          return SecurityCheckResult(
            isValid: false,
            reason: 'ملف حساس مفقود: $filePath',
          );
        }
      }

      return SecurityCheckResult(isValid: true, reason: 'سلامة التطبيق مؤكدة');
    } catch (e) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص سلامة التطبيق: $e',
      );
    }
  }

  /// فحص البيئة
  SecurityCheckResult _checkEnvironment() {
    try {
      // فحص وضع التطوير
      if (kDebugMode) {
        debugPrint('⚠️ التطبيق يعمل في وضع التطوير');
        _securityLevel = SecurityLevel.low;
      }

      // فحص الجذر (Root) على Android
      if (Platform.isAndroid) {
        // محاكاة فحص الجذر
        final isRooted = _checkIfDeviceIsRooted();
        if (isRooted) {
          return SecurityCheckResult(
            isValid: false,
            reason: 'الجهاز مكسور الحماية (Rooted)',
          );
        }
      }

      // فحص Jailbreak على iOS
      if (Platform.isIOS) {
        // محاكاة فحص Jailbreak
        final isJailbroken = _checkIfDeviceIsJailbroken();
        if (isJailbroken) {
          return SecurityCheckResult(
            isValid: false,
            reason: 'الجهاز مكسور الحماية (Jailbroken)',
          );
        }
      }

      return SecurityCheckResult(isValid: true, reason: 'البيئة آمنة');
    } catch (e) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص البيئة: $e',
      );
    }
  }

  /// فحص أمان الشبكة
  Future<SecurityCheckResult> _checkNetworkSecurity() async {
    try {
      // فحص الاتصال المشفر
      final isSecureConnection = await _checkSecureConnection();
      if (!isSecureConnection) {
        return SecurityCheckResult(isValid: false, reason: 'الاتصال غير مشفر');
      }

      // فحص DNS
      final dnsCheck = await _checkDNSSecurity();
      if (!dnsCheck) {
        return SecurityCheckResult(isValid: false, reason: 'DNS غير آمن');
      }

      return SecurityCheckResult(isValid: true, reason: 'الشبكة آمنة');
    } catch (e) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص الشبكة: $e',
      );
    }
  }

  /// فحص أمان الذاكرة
  SecurityCheckResult _checkMemorySecurity() {
    try {
      // فحص تسريب الذاكرة
      final memoryUsage = _getMemoryUsage();
      if (memoryUsage > 500) {
        // MB
        return SecurityCheckResult(
          isValid: false,
          reason: 'استخدام ذاكرة مرتفع: ${memoryUsage}MB',
        );
      }

      return SecurityCheckResult(isValid: true, reason: 'الذاكرة آمنة');
    } catch (e) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص الذاكرة: $e',
      );
    }
  }

  /// فحص معدل الطلبات
  SecurityCheckResult _checkRateLimit() {
    final now = DateTime.now();
    final clientId = 'default'; // يمكن تخصيصه حسب المستخدم

    _requestHistory[clientId] ??= [];

    // إزالة الطلبات القديمة (أكثر من دقيقة)
    _requestHistory[clientId]!.removeWhere(
      (time) => now.difference(time).inMinutes >= 1,
    );

    // إضافة الطلب الحالي
    _requestHistory[clientId]!.add(now);

    // فحص الحد الأقصى
    if (_requestHistory[clientId]!.length > rateLimitPerMinute) {
      return SecurityCheckResult(
        isValid: false,
        reason:
            'تم تجاوز حد معدل الطلبات: ${_requestHistory[clientId]!.length}/$rateLimitPerMinute',
      );
    }

    return SecurityCheckResult(isValid: true, reason: 'معدل الطلبات طبيعي');
  }

  /// فحص الكلمات المحظورة
  SecurityCheckResult _checkBannedWords(String input) {
    final lowerInput = input.toLowerCase();

    for (final word in _bannedWords) {
      if (lowerInput.contains(word.toLowerCase())) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'تحتوي على كلمة محظورة: $word',
        );
      }
    }

    return SecurityCheckResult(isValid: true, reason: 'لا توجد كلمات محظورة');
  }

  /// فحص الأنماط المشبوهة
  SecurityCheckResult _checkSuspiciousPatterns(String input) {
    for (final pattern in _suspiciousPatterns) {
      if (pattern.hasMatch(input)) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'يحتوي على نمط مشبوه: ${pattern.pattern}',
        );
      }
    }

    return SecurityCheckResult(isValid: true, reason: 'لا توجد أنماط مشبوهة');
  }

  /// فحص طول المدخل
  SecurityCheckResult _checkInputLength(String input) {
    const maxLength = 10000; // الحد الأقصى للأحرف

    if (input.length > maxLength) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'المدخل طويل جداً: ${input.length}/$maxLength حرف',
      );
    }

    return SecurityCheckResult(isValid: true, reason: 'طول المدخل مقبول');
  }

  /// فحص المرفقات
  Future<SecurityCheckResult> _checkAttachments(List<File> attachments) async {
    const maxFileSize = 10 * 1024 * 1024; // 10 MB
    const allowedExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.pdf',
      '.txt',
      '.doc',
      '.docx',
    ];

    for (final file in attachments) {
      // فحص حجم الملف
      final fileSize = await file.length();
      if (fileSize > maxFileSize) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'ملف كبير جداً: ${fileSize / (1024 * 1024)} MB',
        );
      }

      // فحص امتداد الملف
      final extension = file.path.toLowerCase().substring(
        file.path.lastIndexOf('.'),
      );
      if (!allowedExtensions.contains(extension)) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'امتداد ملف غير مسموح: $extension',
        );
      }

      // فحص محتوى الملف
      final contentCheck = await _scanFileContent(file);
      if (!contentCheck.isValid) {
        return contentCheck;
      }
    }

    return SecurityCheckResult(isValid: true, reason: 'جميع المرفقات آمنة');
  }

  /// فحص التشفير
  SecurityCheckResult _checkEncryption(String input) {
    // فحص ما إذا كان المدخل يحتوي على بيانات حساسة
    final sensitivePatterns = [
      RegExp(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'), // أرقام بطاقات
      RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'), // إيميلات
      RegExp(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'), // أرقام هواتف
    ];

    for (final pattern in sensitivePatterns) {
      if (pattern.hasMatch(input)) {
        return SecurityCheckResult(
          isValid: false,
          reason: 'يحتوي على بيانات حساسة غير مشفرة',
        );
      }
    }

    return SecurityCheckResult(isValid: true, reason: 'لا توجد بيانات حساسة');
  }

  /// حساب توقيع التطبيق
  Future<String> _calculateAppSignature() async {
    // محاكاة حساب التوقيع
    final random = Random();
    final signature = List.generate(32, (index) => random.nextInt(256));
    return base64Encode(signature);
  }

  /// فحص ما إذا كان الجهاز مكسور الحماية (Android)
  bool _checkIfDeviceIsRooted() {
    // محاكاة فحص الجذر
    return false; // في التطبيق الحقيقي، يتم فحص ملفات النظام
  }

  /// فحص ما إذا كان الجهاز مكسور الحماية (iOS)
  bool _checkIfDeviceIsJailbroken() {
    // محاكاة فحص Jailbreak
    return false; // في التطبيق الحقيقي، يتم فحص ملفات النظام
  }

  /// فحص الاتصال المشفر
  Future<bool> _checkSecureConnection() async {
    // محاكاة فحص HTTPS
    return true;
  }

  /// فحص أمان DNS
  Future<bool> _checkDNSSecurity() async {
    // محاكاة فحص DNS
    return true;
  }

  /// الحصول على استخدام الذاكرة
  double _getMemoryUsage() {
    // محاكاة قراءة استخدام الذاكرة
    return Random().nextDouble() * 200; // MB
  }

  /// فحص محتوى الملف
  Future<SecurityCheckResult> _scanFileContent(File file) async {
    try {
      // فحص بسيط لمحتوى الملف
      final bytes = await file.readAsBytes();

      // فحص التوقيعات الضارة (محاكاة)
      final maliciousSignatures = [
        [0x4D, 0x5A], // PE executable
        [0x7F, 0x45, 0x4C, 0x46], // ELF executable
      ];

      for (final signature in maliciousSignatures) {
        if (bytes.length >= signature.length) {
          bool matches = true;
          for (int i = 0; i < signature.length; i++) {
            if (bytes[i] != signature[i]) {
              matches = false;
              break;
            }
          }
          if (matches) {
            return SecurityCheckResult(
              isValid: false,
              reason: 'ملف قابل للتنفيذ مشبوه',
            );
          }
        }
      }

      return SecurityCheckResult(isValid: true, reason: 'محتوى الملف آمن');
    } catch (e) {
      return SecurityCheckResult(
        isValid: false,
        reason: 'خطأ في فحص محتوى الملف: $e',
      );
    }
  }

  /// الحصول على إحصائيات الأمان
  Map<String, dynamic> getSecurityStats() {
    return {
      ..._securityStats,
      'security_level': _securityLevel.toString(),
      'blocked_ips': _blockedIPs.length,
      'active_sessions': _requestHistory.length,
    };
  }

  /// تحديث مستوى الأمان
  void updateSecurityLevel(SecurityLevel level) {
    _securityLevel = level;
    debugPrint('🔒 تم تحديث مستوى الأمان إلى: $level');
  }
}

/// نتيجة فحص الأمان
class SecurityCheckResult {
  final bool isValid;
  final String reason;
  final SecurityLevel? securityLevel;

  SecurityCheckResult({
    required this.isValid,
    required this.reason,
    this.securityLevel,
  });

  bool get isSecure => isValid;
}

/// نتيجة التحقق من صحة المدخلات
class ValidationResult {
  final bool isValid;
  final String reason;
  final ThreatSeverity severity;

  ValidationResult({
    required this.isValid,
    required this.reason,
    required this.severity,
  });
}

/// مستويات الأمان
enum SecurityLevel {
  low, // منخفض
  medium, // متوسط
  high, // عالي
  critical, // حرج
}

/// مستويات خطورة التهديد
enum ThreatSeverity {
  none, // لا يوجد
  low, // منخفض
  medium, // متوسط
  high, // عالي
  critical, // حرج
}
