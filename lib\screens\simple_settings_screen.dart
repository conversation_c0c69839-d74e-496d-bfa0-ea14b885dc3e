import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../widgets/simple_background.dart';
import '../widgets/simple_bottom_nav.dart';
import '../theme/simple_theme.dart';

/// صفحة الإعدادات المبسطة للأداء المحسن
class SimpleSettingsScreen extends StatelessWidget {
  const SimpleSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const SimpleAppBar(
        title: 'الإعدادات',
        showBackButton: false,
      ),
      body: SimpleBackground(
        child: Consumer<AppProvider>(
          builder: (context, provider, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // قسم التطبيق
                _buildSectionHeader('إعدادات التطبيق'),
                _buildSettingItem(
                  icon: Icons.palette_outlined,
                  title: 'المظهر',
                  subtitle: 'تخصيص شكل التطبيق',
                  onTap: () => _showThemeDialog(context),
                ),
                _buildSettingItem(
                  icon: Icons.language_outlined,
                  title: 'اللغة',
                  subtitle: 'العربية',
                  onTap: () => _showLanguageDialog(context),
                ),
                _buildSettingItem(
                  icon: Icons.notifications_outlined,
                  title: 'الإشعارات',
                  subtitle: 'إدارة الإشعارات',
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {},
                    activeColor: SimpleTheme.primary,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // قسم البيانات
                _buildSectionHeader('إدارة البيانات'),
                _buildSettingItem(
                  icon: Icons.backup_outlined,
                  title: 'النسخ الاحتياطي',
                  subtitle: 'حفظ واستعادة البيانات',
                  onTap: () => Navigator.pushNamed(context, '/backup_manager'),
                ),
                _buildSettingItem(
                  icon: Icons.download_outlined,
                  title: 'تصدير البيانات',
                  subtitle: 'تصدير البيانات كملف Excel',
                  onTap: () => _exportData(context),
                ),
                _buildSettingItem(
                  icon: Icons.delete_outline,
                  title: 'مسح البيانات',
                  subtitle: 'حذف جميع البيانات',
                  onTap: () => _showDeleteDialog(context),
                  isDestructive: true,
                ),
                
                const SizedBox(height: 24),
                
                // قسم التحديثات
                _buildSectionHeader('التحديثات والدعم'),
                _buildSettingItem(
                  icon: Icons.system_update_outlined,
                  title: 'فحص التحديثات',
                  subtitle: 'البحث عن إصدارات جديدة',
                  onTap: () => Navigator.pushNamed(context, '/check_updates'),
                ),
                _buildSettingItem(
                  icon: Icons.help_outline,
                  title: 'المساعدة والدعم',
                  subtitle: 'الحصول على المساعدة',
                  onTap: () => _showHelpDialog(context),
                ),
                _buildSettingItem(
                  icon: Icons.privacy_tip_outlined,
                  title: 'سياسة الخصوصية',
                  subtitle: 'اطلع على سياسة الخصوصية',
                  onTap: () => Navigator.pushNamed(context, '/privacy_policy'),
                ),
                
                const SizedBox(height: 24),
                
                // معلومات التطبيق
                _buildSectionHeader('معلومات التطبيق'),
                _buildInfoItem('الإصدار', '1.0.0'),
                _buildInfoItem('تاريخ الإصدار', '2024-01-01'),
                _buildInfoItem('المطور', 'EduTrack Team'),
                
                const SizedBox(height: 32),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12, top: 8),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: SimpleTheme.primary,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return SimpleCardBackground(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: (isDestructive ? SimpleTheme.danger : SimpleTheme.primary)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isDestructive ? SimpleTheme.danger : SimpleTheme.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDestructive ? SimpleTheme.danger : SimpleTheme.textPrimary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: SimpleTheme.textMuted,
                    ),
                  ),
                ],
              ),
            ),
            trailing ?? const Icon(
              Icons.arrow_forward_ios,
              color: SimpleTheme.textMuted,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return SimpleCardBackground(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: SimpleTheme.textMuted,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: SimpleTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text('المظهر', style: GoogleFonts.cairo(color: SimpleTheme.textPrimary)),
        content: Text('المظهر الداكن مفعل حالياً', style: GoogleFonts.cairo(color: SimpleTheme.textMuted)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حسناً', style: GoogleFonts.cairo(color: SimpleTheme.primary)),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text('اللغة', style: GoogleFonts.cairo(color: SimpleTheme.textPrimary)),
        content: Text('العربية هي اللغة الحالية', style: GoogleFonts.cairo(color: SimpleTheme.textMuted)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حسناً', style: GoogleFonts.cairo(color: SimpleTheme.primary)),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم إضافة هذه الميزة قريباً', style: GoogleFonts.cairo()),
        backgroundColor: SimpleTheme.primary,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text('تحذير', style: GoogleFonts.cairo(color: SimpleTheme.danger)),
        content: Text(
          'هل أنت متأكد من حذف جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo(color: SimpleTheme.textMuted)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حذف', style: GoogleFonts.cairo(color: SimpleTheme.danger)),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text('المساعدة', style: GoogleFonts.cairo(color: SimpleTheme.textPrimary)),
        content: Text(
          'للحصول على المساعدة، يرجى التواصل معنا عبر البريد الإلكتروني:\<EMAIL>',
          style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حسناً', style: GoogleFonts.cairo(color: SimpleTheme.primary)),
          ),
        ],
      ),
    );
  }
}
