import 'package:flutter/material.dart';
import '../models/lesson.dart';
import '../models/reminder.dart';

/// خدمة الإشعارات والتذكيرات المتقدمة (مبسطة)
class NotificationService {
  // مبسط بدون مكتبة خارجية

  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// معالج النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    // معالجة النقر على الإشعار
    print('تم النقر على الإشعار: ${response.payload}');
  }

  /// جدولة تذكير للحصة
  static Future<void> scheduleLessonReminder(
    Lesson lesson,
    Duration beforeStart,
  ) async {
    final scheduledDate = lesson.startTime.subtract(beforeStart);

    if (scheduledDate.isBefore(DateTime.now())) {
      return; // لا يمكن جدولة تذكير في الماضي
    }

    await _notifications.zonedSchedule(
      lesson.id.hashCode,
      'تذكير بالحصة',
      'حصة ${lesson.subject} ستبدأ خلال ${_formatDuration(beforeStart)}',
      _convertToTZDateTime(scheduledDate),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'lesson_reminders',
          'تذكيرات الحصص',
          channelDescription: 'تذكيرات قبل بداية الحصص',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: 'lesson_${lesson.id}',
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// جدولة تذكير للامتحان
  static Future<void> scheduleExamReminder(
    String examName,
    DateTime examDate,
    Duration beforeStart,
  ) async {
    final scheduledDate = examDate.subtract(beforeStart);

    if (scheduledDate.isBefore(DateTime.now())) {
      return;
    }

    await _notifications.zonedSchedule(
      examName.hashCode,
      'تذكير بالامتحان',
      'امتحان $examName سيبدأ خلال ${_formatDuration(beforeStart)}',
      _convertToTZDateTime(scheduledDate),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'exam_reminders',
          'تذكيرات الامتحانات',
          channelDescription: 'تذكيرات قبل بداية الامتحانات',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          color: Colors.red,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: 'exam_$examName',
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// جدولة مهمة يومية
  static Future<void> scheduleDailyTask(
    String taskName,
    String description,
    TimeOfDay time,
  ) async {
    final now = DateTime.now();
    var scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // إذا كان الوقت قد مضى اليوم، جدول للغد
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    await _notifications.zonedSchedule(
      taskName.hashCode,
      'مهمة يومية',
      description,
      _convertToTZDateTime(scheduledDate),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'daily_tasks',
          'المهام اليومية',
          channelDescription: 'تذكيرات بالمهام اليومية',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: 'task_$taskName',
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time, // يتكرر يومياً
    );
  }

  /// منبه ذكي حسب الجدول
  static Future<void> scheduleSmartAlarm(
    List<Lesson> todayLessons,
    Duration wakeUpBuffer,
  ) async {
    if (todayLessons.isEmpty) return;

    // أول حصة في اليوم
    final firstLesson = todayLessons.first;
    final alarmTime = firstLesson.startTime.subtract(wakeUpBuffer);

    if (alarmTime.isBefore(DateTime.now())) {
      return;
    }

    await _notifications.zonedSchedule(
      'smart_alarm'.hashCode,
      'منبه ذكي',
      'حان وقت الاستيقاظ! لديك ${todayLessons.length} حصص اليوم',
      _convertToTZDateTime(alarmTime),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'smart_alarm',
          'المنبه الذكي',
          channelDescription: 'منبه ذكي حسب الجدول اليومي',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          sound: RawResourceAndroidNotificationSound('alarm_sound'),
          playSound: true,
          enableVibration: true,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'alarm_sound.aiff',
        ),
      ),
      payload: 'smart_alarm',
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// إشعار فوري
  static Future<void> showInstantNotification(
    String title,
    String body, {
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      'instant_notifications',
      'الإشعارات الفورية',
      channelDescription: 'إشعارات فورية للأحداث المهمة',
      importance: _getAndroidImportance(priority),
      priority: _getAndroidPriority(priority),
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      NotificationDetails(android: androidDetails, iOS: iosDetails),
      payload: payload,
    );
  }

  /// إلغاء تذكير محدد
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// إلغاء جميع التذكيرات
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// الحصول على التذكيرات المجدولة
  static Future<List<PendingNotificationRequest>>
  getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  /// تحويل DateTime إلى TZDateTime
  static TZDateTime _convertToTZDateTime(DateTime dateTime) {
    // هذا مبسط - في التطبيق الحقيقي يجب استخدام timezone package
    return TZDateTime.from(dateTime, getLocation('Africa/Cairo'));
  }

  /// تنسيق المدة الزمنية
  static String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else {
      return '${duration.inMinutes} دقيقة';
    }
  }

  /// تحويل أولوية الإشعار إلى Android Importance
  static Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  /// تحويل أولوية الإشعار إلى Android Priority
  static Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }
}

/// أولويات الإشعارات
enum NotificationPriority { low, normal, high, urgent }

/// مساعد لإنشاء TZDateTime (مبسط)
class TZDateTime {
  final DateTime dateTime;

  TZDateTime.from(this.dateTime, dynamic location);
}

/// مساعد للحصول على الموقع الزمني (مبسط)
dynamic getLocation(String timezone) {
  return timezone; // مبسط للمثال
}
