import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../lib/ai/edutrack_ai_core.dart';
import '../lib/ai/ai_brain.dart';
import '../lib/ai/ai_memory.dart';
import '../lib/ai/ai_security.dart';
import '../lib/ai/ai_processor.dart';
import '../lib/ai/ai_response_generator.dart';
import '../lib/providers/app_provider.dart';
import '../lib/models/student.dart';
import '../lib/models/group.dart';
import '../lib/models/lesson.dart';

/// اختبارات شاملة لنظام EduTrack AI الجديد
void main() {
  group('EduTrack AI System Tests', () {
    late AppProvider mockProvider;

    setUpAll(() async {
      // تهيئة Hive للاختبارات
      await Hive.initFlutter();
      
      // إنشاء بيانات وهمية للاختبار
      mockProvider = AppProvider();
      await _setupMockData(mockProvider);
    });

    tearDownAll(() async {
      await Hive.close();
    });

    group('AI Brain Tests', () {
      late AIBrain brain;

      setUp(() async {
        brain = AIBrain();
        await brain.initialize();
      });

      test('should understand simple Arabic questions', () async {
        final understanding = await brain.understand(
          'كم عدد الطلاب؟',
          null,
        );

        expect(understanding.type, MessageType.question);
        expect(understanding.entities, contains('طالب'));
        expect(understanding.intent, contains('استعلام'));
        expect(understanding.confidence, greaterThan(0.5));
      });

      test('should understand complex commands', () async {
        final understanding = await brain.understand(
          'أضف طالب جديد اسمه أحمد في مجموعة الرياضيات',
          null,
        );

        expect(understanding.type, MessageType.command);
        expect(understanding.entities, containsAll(['طالب', 'مجموعة']));
        expect(understanding.intent, contains('إضافة'));
        expect(understanding.complexity, ComplexityLevel.medium);
      });

      test('should detect conversational messages', () async {
        final understanding = await brain.understand(
          'مرحباً، كيف حالك؟',
          null,
        );

        expect(understanding.type, MessageType.conversation);
        expect(understanding.intent, contains('محادثة'));
        expect(understanding.sentiment, 'إيجابي');
      });

      test('should extract keywords correctly', () async {
        final understanding = await brain.understand(
          'اعرض تقرير الحضور للطلاب الأسبوع الماضي',
          null,
        );

        expect(understanding.keywords, contains('تقرير'));
        expect(understanding.keywords, contains('حضور'));
        expect(understanding.keywords, contains('طلاب'));
      });

      test('should perform reasoning for complex requests', () async {
        final understanding = await brain.understand(
          'حلل أداء الطلاب واقترح خطة تحسين',
          null,
        );

        expect(understanding.requiresReasoning, true);
        expect(understanding.complexity, ComplexityLevel.high);

        final decision = await brain.reason(understanding, {});
        expect(decision.steps, isNotEmpty);
        expect(decision.reasoning, isNotEmpty);
      });
    });

    group('AI Memory Tests', () {
      late AIMemory memory;
      late String sessionId;

      setUp(() async {
        memory = AIMemory();
        await memory.initialize();
        sessionId = 'test_session_${DateTime.now().millisecondsSinceEpoch}';
      });

      test('should save and retrieve interactions', () async {
        final understanding = AIUnderstanding(
          originalMessage: 'كم عدد الطلاب؟',
          type: MessageType.question,
          intent: 'استعلام_طالب',
          entities: ['طالب'],
          complexity: ComplexityLevel.low,
          requiresReasoning: false,
          confidence: 0.9,
          keywords: ['عدد', 'طلاب'],
          sentiment: 'محايد',
        );

        final decision = AIDecision.simple('استعلام_طالب');

        await memory.saveInteraction(
          sessionId,
          'كم عدد الطلاب؟',
          'عدد الطلاب الإجمالي هو 25 طالب',
          understanding,
          decision,
        );

        final relevantMemory = await memory.getRelevantMemory(
          'طلاب',
          sessionId,
        );

        expect(relevantMemory, isNotEmpty);
        expect(relevantMemory['session_context'], isNotEmpty);
      });

      test('should build session context correctly', () async {
        // إضافة عدة تفاعلات
        for (int i = 0; i < 3; i++) {
          final understanding = AIUnderstanding(
            originalMessage: 'رسالة $i',
            type: MessageType.question,
            intent: 'استعلام_$i',
            entities: ['طالب'],
            complexity: ComplexityLevel.low,
            requiresReasoning: false,
            confidence: 0.8,
            keywords: ['رسالة'],
            sentiment: 'محايد',
          );

          await memory.saveInteraction(
            sessionId,
            'رسالة $i',
            'استجابة $i',
            understanding,
            AIDecision.simple('استعلام_$i'),
          );
        }

        final context = await memory.getRelevantMemory('رسالة', sessionId);
        final sessionContext = context['session_context'] as Map<String, dynamic>;

        expect(sessionContext['recent_interactions'], 3);
        expect(sessionContext['mentioned_entities'], contains('طالب'));
      });

      test('should end session and save analysis', () async {
        await memory.endSession(sessionId);
        // التحقق من أن الجلسة تم إنهاؤها بنجاح
        expect(true, true); // سيتم تحسين هذا الاختبار لاحقاً
      });
    });

    group('AI Security Tests', () {
      late AISecurity security;

      setUp(() {
        security = AISecurity();
      });

      test('should perform system check', () async {
        final result = await security.performSystemCheck();
        expect(result.isSecure, true);
        expect(result.message, isNotEmpty);
      });

      test('should validate safe input', () async {
        final result = await security.validateInput(
          'كم عدد الطلاب في المجموعة؟',
          null,
        );

        expect(result.isValid, true);
        expect(result.threatLevel, ThreatLevel.none);
      });

      test('should block banned words', () async {
        final result = await security.validateInput(
          'أريد اختراق النظام',
          null,
        );

        expect(result.isValid, false);
        expect(result.threatLevel, ThreatLevel.medium);
      });

      test('should detect suspicious patterns', () async {
        final result = await security.validateInput(
          '<script>alert("hack")</script>',
          null,
        );

        expect(result.isValid, false);
        expect(result.threatLevel, ThreatLevel.high);
      });

      test('should limit request rate', () async {
        // محاكاة طلبات متعددة سريعة
        for (int i = 0; i < 35; i++) {
          await security.validateInput('رسالة $i', null);
        }

        final result = await security.validateInput('رسالة إضافية', null);
        expect(result.isValid, false);
        expect(result.message, contains('تجاوز الحد الأقصى'));
      });
    });

    group('AI Processor Tests', () {
      late AIProcessor processor;

      setUp(() async {
        processor = AIProcessor();
        await processor.initialize();
      });

      test('should process empty attachments', () async {
        final result = await processor.processAttachments([]);
        expect(result['processed_count'], 0);
        expect(result['successful_count'], 0);
      });

      test('should handle processing errors gracefully', () async {
        final result = await processor.processAttachments(['invalid_data']);
        expect(result['failed_count'], greaterThan(0));
        expect(result['summary'], contains('فشل'));
      });
    });

    group('AI Response Generator Tests', () {
      late AIResponseGenerator generator;

      setUp(() async {
        generator = AIResponseGenerator();
        await generator.initialize();
      });

      test('should generate template responses for greetings', () async {
        final understanding = AIUnderstanding(
          originalMessage: 'مرحباً',
          type: MessageType.conversation,
          intent: 'تحية',
          entities: [],
          complexity: ComplexityLevel.low,
          requiresReasoning: false,
          confidence: 0.9,
          keywords: ['مرحباً'],
          sentiment: 'إيجابي',
        );

        final decision = AIDecision.simple('تحية');
        final context = <String, dynamic>{};

        final response = await generator.generateResponse(
          understanding,
          decision,
          context,
        );

        expect(response.isSuccess, true);
        expect(response.message, contains('مرحباً'));
        expect(response.type, AIResponseType.text);
      });

      test('should generate action responses for commands', () async {
        final understanding = AIUnderstanding(
          originalMessage: 'أضف طالب جديد',
          type: MessageType.command,
          intent: 'إضافة_طالب',
          entities: ['طالب'],
          complexity: ComplexityLevel.medium,
          requiresReasoning: false,
          confidence: 0.8,
          keywords: ['أضف', 'طالب'],
          sentiment: 'محايد',
        );

        final decision = AIDecision(
          goal: 'إضافة طالب جديد',
          steps: ['جمع المعلومات', 'إنشاء الطالب'],
          requiredData: ['اسم الطالب'],
          actions: [
            {
              'type': 'create_student',
              'target': 'student',
              'parameters': {},
            }
          ],
          reasoning: 'المستخدم يريد إضافة طالب جديد',
          intent: 'إضافة_طالب',
        );

        final context = <String, dynamic>{};

        final response = await generator.generateResponse(
          understanding,
          decision,
          context,
        );

        expect(response.isSuccess, true);
        expect(response.type, AIResponseType.action);
        expect(response.actionData, isNotEmpty);
      });
    });

    group('Integration Tests', () {
      test('should handle complete AI workflow', () async {
        final success = await EduTrackAI.initialize();
        expect(success, true);

        final response = await EduTrackAI.processMessage(
          'كم عدد الطلاب؟',
          mockProvider,
        );

        expect(response.isSuccess, true);
        expect(response.message, isNotEmpty);

        final stats = EduTrackAI.getSystemStats();
        expect(stats['total_interactions'], greaterThan(0));
        expect(stats['is_initialized'], true);
      });

      test('should handle errors gracefully', () async {
        final response = await EduTrackAI.processMessage(
          '', // رسالة فارغة
          mockProvider,
        );

        expect(response.isSuccess, false);
        expect(response.error, isNotEmpty);
      });

      test('should maintain session continuity', () async {
        await EduTrackAI.initialize();

        // إرسال عدة رسائل في نفس الجلسة
        final response1 = await EduTrackAI.processMessage(
          'كم عدد الطلاب؟',
          mockProvider,
        );

        final response2 = await EduTrackAI.processMessage(
          'وكم عدد المجموعات؟',
          mockProvider,
        );

        expect(response1.isSuccess, true);
        expect(response2.isSuccess, true);

        // التحقق من أن الجلسة نفسها
        final sessionId = EduTrackAI.currentSessionId;
        expect(sessionId, isNotNull);
      });
    });

    group('Performance Tests', () {
      test('should respond within reasonable time', () async {
        await EduTrackAI.initialize();

        final stopwatch = Stopwatch()..start();
        
        final response = await EduTrackAI.processMessage(
          'كم عدد الطلاب؟',
          mockProvider,
        );

        stopwatch.stop();

        expect(response.isSuccess, true);
        expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // أقل من 10 ثواني
      });

      test('should handle multiple concurrent requests', () async {
        await EduTrackAI.initialize();

        final futures = List.generate(5, (index) =>
          EduTrackAI.processMessage(
            'رسالة $index',
            mockProvider,
          ),
        );

        final responses = await Future.wait(futures);

        for (final response in responses) {
          expect(response, isNotNull);
        }
      });
    });
  });
}

/// إعداد بيانات وهمية للاختبار
Future<void> _setupMockData(AppProvider provider) async {
  // إضافة مجموعات وهمية
  final group1 = Group(
    id: 'group1',
    name: 'الرياضيات المتقدمة',
    subject: 'رياضيات',
    studentIds: ['student1', 'student2'],
    monthlyFee: 200.0,
  );

  final group2 = Group(
    id: 'group2',
    name: 'الفيزياء الأساسية',
    subject: 'فيزياء',
    studentIds: ['student3'],
    monthlyFee: 150.0,
  );

  provider.addGroup(group1);
  provider.addGroup(group2);

  // إضافة طلاب وهميين
  final student1 = Student(
    id: 'student1',
    name: 'أحمد محمد',
    groupId: 'group1',
    isPresent: true,
    hasPaid: true,
    monthlyPayment: 200.0,
  );

  final student2 = Student(
    id: 'student2',
    name: 'فاطمة علي',
    groupId: 'group1',
    isPresent: false,
    hasPaid: true,
    monthlyPayment: 200.0,
  );

  final student3 = Student(
    id: 'student3',
    name: 'محمد حسن',
    groupId: 'group2',
    isPresent: true,
    hasPaid: false,
    monthlyPayment: 150.0,
  );

  provider.addStudent(student1);
  provider.addStudent(student2);
  provider.addStudent(student3);

  // إضافة دروس وهمية
  final lesson1 = Lesson(
    id: 'lesson1',
    groupId: 'group1',
    dateTime: DateTime.now(),
    isCompleted: true,
    attendedStudentIds: ['student1'],
  );

  final lesson2 = Lesson(
    id: 'lesson2',
    groupId: 'group2',
    dateTime: DateTime.now().add(const Duration(hours: 1)),
    isCompleted: false,
    attendedStudentIds: [],
  );

  provider.addLesson(lesson1);
  provider.addLesson(lesson2);
}
